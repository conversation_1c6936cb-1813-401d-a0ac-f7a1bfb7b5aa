import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Database, RefreshCw } from "lucide-react";

const DatabaseTest = () => {
  const [connectionStatus, setConnectionStatus] = useState('testing');
  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    setConnectionStatus('testing');
    setError(null);

    try {
      // Test de connexion à l'API
      const response = await fetch('http://localhost/api/test-connection.php');
      const result = await response.json();

      if (result.status === 'success') {
        setConnectionStatus('connected');
        setData(result.data);
      } else {
        setConnectionStatus('error');
        setError(result.message);
      }
    } catch (err) {
      setConnectionStatus('error');
      setError('Impossible de se connecter à l\'API. Vérifiez que le serveur PHP est démarré.');
    } finally {
      setLoading(false);
    }
  };

  const testActualites = async () => {
    try {
      const response = await fetch('http://localhost/api/actualites.php');
      const result = await response.json();
      
      if (result.status === 'success') {
        alert(`✅ API Actualités fonctionne ! ${result.count} actualités trouvées.`);
      } else {
        alert('❌ Erreur API Actualités: ' + result.message);
      }
    } catch (err) {
      alert('❌ Erreur de connexion à l\'API Actualités');
    }
  };

  useEffect(() => {
    testConnection();
  }, []);

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'error':
        return <XCircle className="h-6 w-6 text-red-500" />;
      default:
        return <RefreshCw className={`h-6 w-6 text-blue-500 ${loading ? 'animate-spin' : ''}`} />;
    }
  };

  const getStatusBadge = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Badge className="bg-green-500">Connecté</Badge>;
      case 'error':
        return <Badge variant="destructive">Erreur</Badge>;
      default:
        return <Badge variant="secondary">Test en cours...</Badge>;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-6 w-6" />
            Test de connexion à la base de données
          </CardTitle>
          <CardDescription>
            Vérifiez si votre site est correctement connecté à la base de données MySQL
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStatusIcon()}
              <span className="font-medium">Statut de la connexion:</span>
              {getStatusBadge()}
            </div>
            <Button onClick={testConnection} disabled={loading}>
              {loading ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : null}
              Retester
            </Button>
          </div>

          {connectionStatus === 'connected' && data && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 mb-2">✅ Connexion réussie !</h3>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">Actualités:</span>
                  <span className="ml-2 text-green-700">{data.actualites}</span>
                </div>
                <div>
                  <span className="font-medium">Formations:</span>
                  <span className="ml-2 text-green-700">{data.formations}</span>
                </div>
                <div>
                  <span className="font-medium">Événements:</span>
                  <span className="ml-2 text-green-700">{data.evenements}</span>
                </div>
              </div>
            </div>
          )}

          {connectionStatus === 'error' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="font-semibold text-red-800 mb-2">❌ Erreur de connexion</h3>
              <p className="text-red-700 text-sm">{error}</p>
              <div className="mt-3 text-sm text-red-600">
                <p><strong>Solutions possibles:</strong></p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Vérifiez que MySQL/XAMPP est démarré</li>
                  <li>Vérifiez que la base de données 'cmc_agriculture' existe</li>
                  <li>Vérifiez les paramètres de connexion dans api/config/database.php</li>
                  <li>Vérifiez que le dossier 'api' est accessible via http://localhost/api/</li>
                </ul>
              </div>
            </div>
          )}

          {connectionStatus === 'connected' && (
            <div className="border-t pt-4">
              <h3 className="font-semibold mb-3">Tests des APIs</h3>
              <div className="space-y-2">
                <Button onClick={testActualites} variant="outline" size="sm">
                  Tester API Actualités
                </Button>
                <p className="text-sm text-muted-foreground">
                  Testez si l'API peut récupérer les actualités de la base de données
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Instructions de configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-semibold">1. Serveur web requis</h4>
            <p className="text-sm text-muted-foreground">
              Installez XAMPP, WAMP, ou MAMP pour avoir Apache + MySQL + PHP
            </p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-semibold">2. Placez les fichiers API</h4>
            <p className="text-sm text-muted-foreground">
              Copiez le dossier 'api' dans votre dossier web (htdocs pour XAMPP)
            </p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-semibold">3. Créez la base de données</h4>
            <p className="text-sm text-muted-foreground">
              Utilisez phpMyAdmin pour importer le fichier database/schema.sql
            </p>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-semibold">4. Configurez la connexion</h4>
            <p className="text-sm text-muted-foreground">
              Modifiez api/config/database.php avec vos paramètres MySQL
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DatabaseTest;
