<?php
// Test de connexion à la base de données
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        // Test de requête simple
        $query = "SELECT COUNT(*) as total_actualites FROM actualites";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $query2 = "SELECT COUNT(*) as total_formations FROM formations";
        $stmt2 = $db->prepare($query2);
        $stmt2->execute();
        $result2 = $stmt2->fetch(PDO::FETCH_ASSOC);
        
        $query3 = "SELECT COUNT(*) as total_evenements FROM evenements";
        $stmt3 = $db->prepare($query3);
        $stmt3->execute();
        $result3 = $stmt3->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'status' => 'success',
            'message' => 'Connexion à la base de données réussie !',
            'data' => [
                'actualites' => $result['total_actualites'],
                'formations' => $result2['total_formations'],
                'evenements' => $result3['total_evenements']
            ],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } else {
        echo json_encode([
            'status' => 'error',
            'message' => 'Impossible de se connecter à la base de données'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Erreur: ' . $e->getMessage()
    ]);
}
?>
