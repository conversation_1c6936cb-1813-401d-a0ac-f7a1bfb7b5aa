-- =====================================================
-- BASE DE DONNÉES - PÔLE AGRICULTURE CMC
-- Schéma complet pour le site web
-- =====================================================

-- Suppression des tables existantes (ordre inverse des dépendances)
DROP TABLE IF EXISTS inscriptions;
DROP TABLE IF EXISTS formation_modules;
DROP TABLE IF EXISTS emplois_du_temps;
DROP TABLE IF EXISTS evenements;
DROP TABLE IF EXISTS formations;
DROP TABLE IF EXISTS actualites;
DROP TABLE IF EXISTS groupes;
DROP TABLE IF EXISTS filieres;
DROP TABLE IF EXISTS categories;
DROP TABLE IF EXISTS users;

-- =====================================================
-- TABLE DES UTILISATEURS
-- =====================================================
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    role ENUM('admin', 'etudiant', 'formateur') NOT NULL DEFAULT 'etudiant',
    telephone VARCHAR(20),
    date_naissance DATE,
    adresse TEXT,
    filiere_id INT,
    groupe_id INT,
    statut ENUM('actif', 'inactif', 'diplome') DEFAULT 'actif',
    date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_statut (statut)
);

-- =====================================================
-- TABLES DE RÉFÉRENCE
-- =====================================================

-- Table des catégories (pour actualités et formations)
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    couleur VARCHAR(7), -- Code couleur hexadécimal
    type ENUM('actualite', 'formation', 'evenement') NOT NULL,
    ordre_affichage INT DEFAULT 0,
    actif BOOLEAN DEFAULT TRUE
);

-- Table des filières
CREATE TABLE filieres (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    duree_standard VARCHAR(20), -- ex: "24 mois"
    niveau_requis VARCHAR(100),
    actif BOOLEAN DEFAULT TRUE,
    ordre_affichage INT DEFAULT 0
);

-- Table des groupes
CREATE TABLE groupes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filiere_id INT NOT NULL,
    code VARCHAR(50) NOT NULL,
    nom VARCHAR(100) NOT NULL,
    capacite_max INT DEFAULT 25,
    annee_scolaire VARCHAR(20), -- ex: "2024-2025"
    actif BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (filiere_id) REFERENCES filieres(id) ON DELETE CASCADE,
    UNIQUE KEY unique_groupe_filiere (filiere_id, code, annee_scolaire),
    INDEX idx_filiere (filiere_id),
    INDEX idx_actif (actif)
);

-- =====================================================
-- TABLE DES ACTUALITÉS
-- =====================================================
CREATE TABLE actualites (
    id INT PRIMARY KEY AUTO_INCREMENT,
    titre VARCHAR(255) NOT NULL,
    resume TEXT NOT NULL,
    contenu LONGTEXT NOT NULL,
    category_id INT,
    statut ENUM('brouillon', 'publie') DEFAULT 'brouillon',
    auteur VARCHAR(100) NOT NULL,
    temps_lecture VARCHAR(20), -- ex: "3 min"
    image_url TEXT,
    date_publication DATE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    vues INT DEFAULT 0,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON SET NULL,
    INDEX idx_statut (statut),
    INDEX idx_date_publication (date_publication),
    INDEX idx_category (category_id),
    FULLTEXT idx_recherche (titre, resume, contenu)
);

-- =====================================================
-- TABLE DES ÉVÉNEMENTS
-- =====================================================
CREATE TABLE evenements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date_evenement DATE NOT NULL,
    heure_debut TIME,
    heure_fin TIME,
    lieu VARCHAR(255) NOT NULL,
    category_id INT,
    statut ENUM('brouillon', 'publie') DEFAULT 'brouillon',
    image_url TEXT,
    places_max INT,
    places_reservees INT DEFAULT 0,
    prix DECIMAL(10,2) DEFAULT 0.00,
    inscription_requise BOOLEAN DEFAULT FALSE,
    date_limite_inscription DATE,
    contact_email VARCHAR(255),
    contact_telephone VARCHAR(20),
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON SET NULL,
    INDEX idx_date_evenement (date_evenement),
    INDEX idx_statut (statut),
    INDEX idx_category (category_id)
);

-- =====================================================
-- TABLE DES FORMATIONS
-- =====================================================
CREATE TABLE formations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    titre VARCHAR(255) NOT NULL,
    description LONGTEXT NOT NULL,
    category_id INT,
    niveau ENUM('Qualification', 'Technicien', 'Technicien Spécialisé') NOT NULL,
    duree VARCHAR(50) NOT NULL, -- ex: "24 mois"
    prerequis TEXT NOT NULL,
    debouches TEXT NOT NULL,
    date_debut VARCHAR(50), -- ex: "Septembre 2025"
    places_max INT NOT NULL,
    frais DECIMAL(10,2) NOT NULL,
    statut ENUM('brouillon', 'publie') DEFAULT 'brouillon',
    badge VARCHAR(50), -- ex: "Populaire", "Nouvelle session"
    image_url TEXT,
    filiere_id INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON SET NULL,
    FOREIGN KEY (filiere_id) REFERENCES filieres(id) ON SET NULL,
    INDEX idx_statut (statut),
    INDEX idx_niveau (niveau),
    INDEX idx_category (category_id),
    INDEX idx_filiere (filiere_id),
    FULLTEXT idx_recherche (titre, description)
);

-- =====================================================
-- TABLE DES MODULES DE FORMATION
-- =====================================================
CREATE TABLE formation_modules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    formation_id INT NOT NULL,
    nom VARCHAR(255) NOT NULL,
    description TEXT,
    duree_heures INT,
    ordre_affichage INT DEFAULT 0,
    obligatoire BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    INDEX idx_formation (formation_id),
    INDEX idx_ordre (ordre_affichage)
);

-- =====================================================
-- TABLE DES EMPLOIS DU TEMPS
-- =====================================================
CREATE TABLE emplois_du_temps (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filiere_id INT NOT NULL,
    groupe_id INT NOT NULL,
    nom_fichier VARCHAR(255) NOT NULL,
    url_fichier TEXT NOT NULL,
    type_fichier ENUM('image', 'pdf') NOT NULL,
    taille_fichier INT, -- en bytes
    semaine_debut DATE,
    semaine_fin DATE,
    annee_scolaire VARCHAR(20), -- ex: "2024-2025"
    actif BOOLEAN DEFAULT TRUE,
    date_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (filiere_id) REFERENCES filieres(id) ON DELETE CASCADE,
    FOREIGN KEY (groupe_id) REFERENCES groupes(id) ON DELETE CASCADE,
    INDEX idx_filiere_groupe (filiere_id, groupe_id),
    INDEX idx_actif (actif),
    INDEX idx_annee (annee_scolaire)
);

-- =====================================================
-- TABLE DES INSCRIPTIONS
-- =====================================================
CREATE TABLE inscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    formation_id INT,
    evenement_id INT,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(20),
    niveau_etudes VARCHAR(100),
    motivation TEXT,
    statut ENUM('en_attente', 'accepte', 'refuse', 'liste_attente') DEFAULT 'en_attente',
    date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    notes_admin TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON SET NULL,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON SET NULL,
    FOREIGN KEY (evenement_id) REFERENCES evenements(id) ON SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_formation (formation_id),
    INDEX idx_evenement (evenement_id),
    INDEX idx_statut (statut),
    INDEX idx_date_inscription (date_inscription),
    CHECK (formation_id IS NOT NULL OR evenement_id IS NOT NULL)
);

-- =====================================================
-- CONTRAINTES DE CLÉS ÉTRANGÈRES POUR USERS
-- =====================================================
ALTER TABLE users
ADD FOREIGN KEY (filiere_id) REFERENCES filieres(id) ON SET NULL,
ADD FOREIGN KEY (groupe_id) REFERENCES groupes(id) ON SET NULL;

-- =====================================================
-- INSERTION DES DONNÉES D'EXEMPLE
-- =====================================================

-- Insertion des catégories
INSERT INTO categories (code, nom, description, couleur, type, ordre_affichage) VALUES
('infrastructure', 'Infrastructure', 'Nouveaux équipements et installations', '#3B82F6', 'actualite', 1),
('evenement', 'Événement', 'Annonces d\'événements à venir', '#10B981', 'actualite', 2),
('formation', 'Formation', 'Nouvelles formations et programmes', '#F59E0B', 'actualite', 3),
('partenariat', 'Partenariat', 'Accords et collaborations', '#8B5CF6', 'actualite', 4),
('reussite', 'Réussite', 'Succès des étudiants et projets', '#EF4444', 'actualite', 5),
('culture', 'Culture', 'Production végétale', '#22C55E', 'formation', 1),
('elevage', 'Élevage', 'Production animale', '#F97316', 'formation', 2),
('gestion', 'Gestion', 'Gestion d\'exploitation', '#6366F1', 'formation', 3),
('durable', 'Agriculture durable', 'Pratiques durables', '#84CC16', 'formation', 4),
('portes-ouvertes', 'Portes ouvertes', 'Journées de découverte', '#06B6D4', 'evenement', 1),
('conference', 'Conférence', 'Événements académiques', '#8B5CF6', 'evenement', 2),
('atelier', 'Atelier', 'Sessions pratiques', '#F59E0B', 'evenement', 3),
('forum', 'Forum', 'Rencontres professionnelles', '#EF4444', 'evenement', 4);

-- Insertion des filières
INSERT INTO filieres (code, nom, description, duree_standard, niveau_requis, ordre_affichage) VALUES
('culture', 'Culture', 'Formation en production végétale : cultures maraîchères, céréalières et arboricoles', '24 mois', 'Niveau baccalauréat', 1),
('elevage', 'Élevage', 'Formation en production animale : bovins, ovins, caprins, aviculture', '30 mois', 'Niveau baccalauréat', 2),
('gestion', 'Gestion d\'exploitation', 'Formation en gestion technique et économique des exploitations agricoles', '24 mois', 'Niveau baccalauréat + expérience', 3),
('durable', 'Agriculture durable', 'Formation aux pratiques agricoles respectueuses de l\'environnement', '18 mois', 'Niveau baccalauréat', 4);

-- Insertion des groupes
INSERT INTO groupes (filiere_id, code, nom, capacite_max, annee_scolaire) VALUES
(1, 'c1', 'Culture - Groupe 1', 25, '2024-2025'),
(1, 'c2', 'Culture - Groupe 2', 25, '2024-2025'),
(2, 'e1', 'Élevage - Groupe 1', 20, '2024-2025'),
(2, 'e2', 'Élevage - Groupe 2', 20, '2024-2025'),
(3, 'g1', 'Gestion - Groupe 1', 15, '2024-2025'),
(4, 'd1', 'Agriculture durable - Groupe 1', 20, '2024-2025'),
(4, 'd2', 'Agriculture durable - Groupe 2', 20, '2024-2025');

-- Insertion des utilisateurs (admin et exemples)
INSERT INTO users (email, password_hash, nom, prenom, role, telephone, filiere_id, groupe_id) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'Système', 'admin', '+212 5XX XX XX XX', NULL, NULL),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Benali', 'Khadija', 'formateur', '+212 6XX XX XX XX', 1, NULL),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Alami', 'Youssef', 'etudiant', '+212 7XX XX XX XX', 1, 1),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Tazi', 'Fatima', 'etudiant', '+212 6XX XX XX XX', 2, 3);

-- Insertion des actualités
INSERT INTO actualites (titre, resume, contenu, category_id, statut, auteur, temps_lecture, image_url, date_publication) VALUES
('Inauguration du nouveau laboratoire d\'analyse des sols',
 'Un nouveau laboratoire d\'analyse des sols a été inauguré au sein du Pôle Agriculture, renforçant les capacités de recherche et de formation.',
 'Le Pôle Agriculture de la Cité des Métiers et des Compétences a inauguré ce mardi un nouveau laboratoire d\'analyse des sols, équipé des technologies les plus récentes pour l\'étude et l\'analyse des propriétés physiques, chimiques et biologiques des sols. Cette infrastructure moderne permettra aux stagiaires d\'acquérir une expertise pratique dans l\'analyse des sols, compétence essentielle pour optimiser les rendements agricoles.',
 1, 'publie', 'Service communication', '3 min',
 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
 '2024-03-20'),

('Journée portes ouvertes: venez découvrir nos formations agricoles',
 'Le Pôle Agriculture organise une journée portes ouvertes le 15 avril 2025. Une occasion unique de découvrir nos installations et nos programmes de formation.',
 'Le Pôle Agriculture de la CMC ouvre ses portes au public le 15 avril prochain. Cette journée sera l\'occasion pour les futurs stagiaires, leurs familles et toute personne intéressée par le secteur agricole de découvrir notre campus, nos installations techniques et nos différentes filières de formation. Au programme : visites guidées, démonstrations pratiques, rencontres avec les formateurs et informations sur les processus d\'admission.',
 2, 'publie', 'Service communication', '4 min',
 'https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
 '2024-04-15'),

('Partenariat avec l\'Université Mohammed VI Polytechnique',
 'Signature d\'un accord de partenariat pour développer des programmes de recherche conjoints en agriculture durable.',
 'Un nouveau partenariat stratégique a été signé avec l\'Université Mohammed VI Polytechnique pour développer des programmes de recherche conjoints en agriculture durable et former les futurs experts du secteur. Cette collaboration permettra aux stagiaires d\'accéder à des ressources de recherche avancées et de participer à des projets innovants.',
 4, 'brouillon', 'Direction', '2 min',
 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
 '2024-03-10');

-- Insertion des événements
INSERT INTO evenements (titre, description, date_evenement, heure_debut, heure_fin, lieu, category_id, statut, image_url, places_max, inscription_requise) VALUES
('Journée portes ouvertes',
 'Découvrez nos formations et nos installations lors de notre journée portes ouvertes annuelle.',
 '2025-04-15', '09:00:00', '17:00:00', 'Campus CMC Agriculture', 10, 'publie',
 'https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
 200, TRUE),

('Conférence internationale sur l\'agriculture du futur',
 'Une journée d\'échanges et de conférences sur l\'avenir de l\'agriculture au Maroc et en Afrique.',
 '2025-05-05', '08:30:00', '18:00:00', 'Auditorium principal CMC', 11, 'publie',
 'https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
 150, TRUE),

('Atelier pratique: Techniques d\'agriculture urbaine',
 'Apprenez les bases de l\'agriculture urbaine et les techniques adaptées aux petits espaces.',
 '2025-05-20', '14:00:00', '17:00:00', 'Serre pédagogique', 12, 'brouillon',
 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
 30, TRUE);

-- Insertion des formations
INSERT INTO formations (titre, description, category_id, niveau, duree, prerequis, debouches, date_debut, places_max, frais, statut, badge, image_url, filiere_id) VALUES
('Technicien en Production Végétale',
 'Formation complète en production végétale couvrant les cultures maraîchères, céréalières et arboricoles avec les techniques modernes de production.',
 6, 'Technicien', '24 mois',
 'Niveau baccalauréat scientifique ou technique',
 'Technicien capable de gérer une exploitation agricole spécialisée en production végétale',
 'Septembre 2025', 25, 1500.00, 'publie', 'Populaire',
 'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
 1),

('Technicien Spécialisé en Élevage',
 'Formation avancée en élevage couvrant tous les aspects de la production animale : bovins, ovins, caprins, aviculture et apiculture.',
 7, 'Technicien Spécialisé', '30 mois',
 'Niveau baccalauréat, de préférence scientifique',
 'Technicien spécialisé en élevage capable de gérer des exploitations d\'élevage modernes',
 'Octobre 2025', 20, 1500.00, 'publie', 'Nouvelle session',
 'https://images.unsplash.com/photo-1500595046743-cd271d694d30?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
 2),

('Gestionnaire d\'Exploitation Agricole',
 'Formation en gestion d\'exploitation agricole combinant aspects techniques, économiques et managériaux pour une gestion optimale des ressources.',
 8, 'Technicien Spécialisé', '24 mois',
 'Niveau baccalauréat, expérience en agriculture appréciée',
 'Gestionnaire capable de diriger une exploitation agricole moderne et rentable',
 'Septembre 2025', 15, 1500.00, 'brouillon', '',
 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
 3);

-- Insertion des modules de formation
INSERT INTO formation_modules (formation_id, nom, ordre_affichage) VALUES
-- Modules pour Technicien en Production Végétale (formation_id = 1)
(1, 'Agronomie générale et pédologie', 1),
(1, 'Production des cultures maraîchères', 2),
(1, 'Production des cultures céréalières', 3),
(1, 'Arboriculture fruitière', 4),
(1, 'Protection phytosanitaire', 5),
(1, 'Irrigation et fertilisation', 6),
(1, 'Mécanisation agricole', 7),
(1, 'Post-récolte et conditionnement', 8),

-- Modules pour Technicien Spécialisé en Élevage (formation_id = 2)
(2, 'Zootechnie générale', 1),
(2, 'Nutrition et alimentation animale', 2),
(2, 'Reproduction et amélioration génétique', 3),
(2, 'Pathologie et prophylaxie', 4),
(2, 'Gestion technico-économique d\'élevage', 5),
(2, 'Transformation des produits animaux', 6),
(2, 'Commercialisation des produits d\'élevage', 7),
(2, 'Législation et réglementation', 8),

-- Modules pour Gestionnaire d'Exploitation Agricole (formation_id = 3)
(3, 'Économie et gestion d\'entreprise agricole', 1),
(3, 'Comptabilité et analyse financière', 2),
(3, 'Marketing et commercialisation', 3),
(3, 'Gestion des ressources humaines', 4),
(3, 'Techniques de production agricole', 5),
(3, 'Gestion de la qualité et traçabilité', 6),
(3, 'Informatique appliquée à l\'agriculture', 7),
(3, 'Législation agricole et foncière', 8);

-- Insertion des emplois du temps
INSERT INTO emplois_du_temps (filiere_id, groupe_id, nom_fichier, url_fichier, type_fichier, annee_scolaire) VALUES
(1, 1, 'culture-c1.png', '/images/emplois-du-temps/culture-c1.png', 'image', '2024-2025'),
(2, 3, 'elevage-e1.png', '/images/emplois-du-temps/elevage-e1.png', 'image', '2024-2025'),
(3, 5, 'gestion-g1.pdf', '/images/emplois-du-temps/gestion-g1.pdf', 'pdf', '2024-2025'),
(4, 6, 'durable-d1.png', '/images/emplois-du-temps/durable-d1.png', 'image', '2024-2025');

-- Insertion d'exemples d'inscriptions
INSERT INTO inscriptions (formation_id, nom, prenom, email, telephone, niveau_etudes, motivation, statut) VALUES
(1, 'Bennani', 'Ahmed', '<EMAIL>', '+212 6XX XX XX XX', 'Baccalauréat Sciences', 'Passionné par l\'agriculture moderne et les nouvelles technologies agricoles.', 'en_attente'),
(2, 'Alaoui', 'Zineb', '<EMAIL>', '+212 7XX XX XX XX', 'Baccalauréat Technique', 'Intéressée par l\'élevage et la production animale durable.', 'accepte'),
(1, 'Idrissi', 'Omar', '<EMAIL>', '+212 6XX XX XX XX', 'Baccalauréat Sciences', 'Souhaite développer une exploitation agricole familiale.', 'accepte');

-- =====================================================
-- VUES UTILES POUR L'APPLICATION
-- =====================================================

-- Vue pour les formations avec leurs catégories et filières
CREATE VIEW v_formations_completes AS
SELECT
    f.id,
    f.titre,
    f.description,
    f.niveau,
    f.duree,
    f.prerequis,
    f.debouches,
    f.date_debut,
    f.places_max,
    f.frais,
    f.statut,
    f.badge,
    f.image_url,
    c.nom as categorie_nom,
    c.code as categorie_code,
    fil.nom as filiere_nom,
    fil.code as filiere_code,
    COUNT(fm.id) as nombre_modules
FROM formations f
LEFT JOIN categories c ON f.category_id = c.id
LEFT JOIN filieres fil ON f.filiere_id = fil.id
LEFT JOIN formation_modules fm ON f.id = fm.formation_id
GROUP BY f.id;

-- Vue pour les actualités avec leurs catégories
CREATE VIEW v_actualites_completes AS
SELECT
    a.id,
    a.titre,
    a.resume,
    a.contenu,
    a.statut,
    a.auteur,
    a.temps_lecture,
    a.image_url,
    a.date_publication,
    a.date_creation,
    a.vues,
    c.nom as categorie_nom,
    c.code as categorie_code,
    c.couleur as categorie_couleur
FROM actualites a
LEFT JOIN categories c ON a.category_id = c.id;

-- Vue pour les événements avec leurs catégories
CREATE VIEW v_evenements_complets AS
SELECT
    e.id,
    e.titre,
    e.description,
    e.date_evenement,
    e.heure_debut,
    e.heure_fin,
    e.lieu,
    e.statut,
    e.image_url,
    e.places_max,
    e.places_reservees,
    e.prix,
    e.inscription_requise,
    c.nom as categorie_nom,
    c.code as categorie_code
FROM evenements e
LEFT JOIN categories c ON e.category_id = c.id;
