-- =====================================================
-- BASE DE DONNÉES - PÔLE AGRICULTURE CMC
-- Schéma complet pour le site web
-- =====================================================

-- Suppression des tables existantes (ordre inverse des dépendances)
DROP TABLE IF EXISTS inscriptions;
DROP TABLE IF EXISTS formation_modules;
DROP TABLE IF EXISTS emplois_du_temps;
DROP TABLE IF EXISTS evenements;
DROP TABLE IF EXISTS formations;
DROP TABLE IF EXISTS actualites;
DROP TABLE IF EXISTS groupes;
DROP TABLE IF EXISTS filieres;
DROP TABLE IF EXISTS categories;
DROP TABLE IF EXISTS users;

-- =====================================================
-- TABLE DES UTILISATEURS
-- =====================================================
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    role ENUM('admin', 'etudiant', 'formateur') NOT NULL DEFAULT 'etudiant',
    telephone VARCHAR(20),
    date_naissance DATE,
    adresse TEXT,
    filiere_id INT,
    groupe_id INT,
    statut ENUM('actif', 'inactif', 'diplome') DEFAULT 'actif',
    date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_statut (statut)
);

-- =====================================================
-- TABLES DE RÉFÉRENCE
-- =====================================================

-- Table des catégories (pour actualités et formations)
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    couleur VARCHAR(7), -- Code couleur hexadécimal
    type ENUM('actualite', 'formation', 'evenement') NOT NULL,
    ordre_affichage INT DEFAULT 0,
    actif BOOLEAN DEFAULT TRUE
);

-- Table des filières
CREATE TABLE filieres (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    duree_standard VARCHAR(20), -- ex: "24 mois"
    niveau_requis VARCHAR(100),
    actif BOOLEAN DEFAULT TRUE,
    ordre_affichage INT DEFAULT 0
);

-- Table des groupes
CREATE TABLE groupes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filiere_id INT NOT NULL,
    code VARCHAR(50) NOT NULL,
    nom VARCHAR(100) NOT NULL,
    capacite_max INT DEFAULT 25,
    annee_scolaire VARCHAR(20), -- ex: "2024-2025"
    actif BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (filiere_id) REFERENCES filieres(id) ON DELETE CASCADE,
    UNIQUE KEY unique_groupe_filiere (filiere_id, code, annee_scolaire),
    INDEX idx_filiere (filiere_id),
    INDEX idx_actif (actif)
);

-- =====================================================
-- TABLE DES ACTUALITÉS
-- =====================================================
CREATE TABLE actualites (
    id INT PRIMARY KEY AUTO_INCREMENT,
    titre VARCHAR(255) NOT NULL,
    resume TEXT NOT NULL,
    contenu LONGTEXT NOT NULL,
    category_id INT,
    statut ENUM('brouillon', 'publie') DEFAULT 'brouillon',
    auteur VARCHAR(100) NOT NULL,
    temps_lecture VARCHAR(20), -- ex: "3 min"
    image_url TEXT,
    date_publication DATE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    vues INT DEFAULT 0,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON SET NULL,
    INDEX idx_statut (statut),
    INDEX idx_date_publication (date_publication),
    INDEX idx_category (category_id),
    FULLTEXT idx_recherche (titre, resume, contenu)
);

-- =====================================================
-- TABLE DES ÉVÉNEMENTS
-- =====================================================
CREATE TABLE evenements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    titre VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    date_evenement DATE NOT NULL,
    heure_debut TIME,
    heure_fin TIME,
    lieu VARCHAR(255) NOT NULL,
    category_id INT,
    statut ENUM('brouillon', 'publie') DEFAULT 'brouillon',
    image_url TEXT,
    places_max INT,
    places_reservees INT DEFAULT 0,
    prix DECIMAL(10,2) DEFAULT 0.00,
    inscription_requise BOOLEAN DEFAULT FALSE,
    date_limite_inscription DATE,
    contact_email VARCHAR(255),
    contact_telephone VARCHAR(20),
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON SET NULL,
    INDEX idx_date_evenement (date_evenement),
    INDEX idx_statut (statut),
    INDEX idx_category (category_id)
);

-- =====================================================
-- TABLE DES FORMATIONS
-- =====================================================
CREATE TABLE formations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    titre VARCHAR(255) NOT NULL,
    description LONGTEXT NOT NULL,
    category_id INT,
    niveau ENUM('Qualification', 'Technicien', 'Technicien Spécialisé') NOT NULL,
    duree VARCHAR(50) NOT NULL, -- ex: "24 mois"
    prerequis TEXT NOT NULL,
    debouches TEXT NOT NULL,
    date_debut VARCHAR(50), -- ex: "Septembre 2025"
    places_max INT NOT NULL,
    frais DECIMAL(10,2) NOT NULL,
    statut ENUM('brouillon', 'publie') DEFAULT 'brouillon',
    badge VARCHAR(50), -- ex: "Populaire", "Nouvelle session"
    image_url TEXT,
    filiere_id INT,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON SET NULL,
    FOREIGN KEY (filiere_id) REFERENCES filieres(id) ON SET NULL,
    INDEX idx_statut (statut),
    INDEX idx_niveau (niveau),
    INDEX idx_category (category_id),
    INDEX idx_filiere (filiere_id),
    FULLTEXT idx_recherche (titre, description)
);

-- =====================================================
-- TABLE DES MODULES DE FORMATION
-- =====================================================
CREATE TABLE formation_modules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    formation_id INT NOT NULL,
    nom VARCHAR(255) NOT NULL,
    description TEXT,
    duree_heures INT,
    ordre_affichage INT DEFAULT 0,
    obligatoire BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON DELETE CASCADE,
    INDEX idx_formation (formation_id),
    INDEX idx_ordre (ordre_affichage)
);

-- =====================================================
-- TABLE DES EMPLOIS DU TEMPS
-- =====================================================
CREATE TABLE emplois_du_temps (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filiere_id INT NOT NULL,
    groupe_id INT NOT NULL,
    nom_fichier VARCHAR(255) NOT NULL,
    url_fichier TEXT NOT NULL,
    type_fichier ENUM('image', 'pdf') NOT NULL,
    taille_fichier INT, -- en bytes
    semaine_debut DATE,
    semaine_fin DATE,
    annee_scolaire VARCHAR(20), -- ex: "2024-2025"
    actif BOOLEAN DEFAULT TRUE,
    date_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (filiere_id) REFERENCES filieres(id) ON DELETE CASCADE,
    FOREIGN KEY (groupe_id) REFERENCES groupes(id) ON DELETE CASCADE,
    INDEX idx_filiere_groupe (filiere_id, groupe_id),
    INDEX idx_actif (actif),
    INDEX idx_annee (annee_scolaire)
);

-- =====================================================
-- TABLE DES INSCRIPTIONS
-- =====================================================
CREATE TABLE inscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    formation_id INT,
    evenement_id INT,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telephone VARCHAR(20),
    niveau_etudes VARCHAR(100),
    motivation TEXT,
    statut ENUM('en_attente', 'accepte', 'refuse', 'liste_attente') DEFAULT 'en_attente',
    date_inscription TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_traitement TIMESTAMP NULL,
    notes_admin TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON SET NULL,
    FOREIGN KEY (formation_id) REFERENCES formations(id) ON SET NULL,
    FOREIGN KEY (evenement_id) REFERENCES evenements(id) ON SET NULL,
    INDEX idx_user (user_id),
    INDEX idx_formation (formation_id),
    INDEX idx_evenement (evenement_id),
    INDEX idx_statut (statut),
    INDEX idx_date_inscription (date_inscription),
    CHECK (formation_id IS NOT NULL OR evenement_id IS NOT NULL)
);

-- =====================================================
-- CONTRAINTES DE CLÉS ÉTRANGÈRES POUR USERS
-- =====================================================
ALTER TABLE users 
ADD FOREIGN KEY (filiere_id) REFERENCES filieres(id) ON SET NULL,
ADD FOREIGN KEY (groupe_id) REFERENCES groupes(id) ON SET NULL;
