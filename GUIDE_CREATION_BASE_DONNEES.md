# Guide complet - Création de la base de données CMC Agriculture

## 🎯 Vue d'ensemble

Ce guide vous explique comment créer et configurer la base de données complète pour le site web du Pôle Agriculture CMC. La base de données contient toutes les tables nécessaires avec des données d'exemple.

## 📋 Prérequis

### Logiciels nécessaires :
1. **MySQL Server** (version 8.0 ou supérieure) ou **MariaDB**
2. **phpMyAdmin** (interface web) ou **MySQL Workbench** (application desktop)
3. Ou **ligne de commande MySQL**

### Fichiers requis :
- `database/schema.sql` (schéma complet avec données)

## 🚀 Méthode 1 : Avec phpMyAdmin (Recommandée)

### Étape 1 : Accéder à phpMyAdmin
1. Ouvrir votre navigateur
2. Aller à `http://localhost/phpmyadmin`
3. Se connecter avec vos identifiants MySQL

### Étape 2 : Créer la base de données
1. C<PERSON>r sur "Nouvelle base de données" dans le menu de gauche
2. Nom de la base : `cmc_agriculture`
3. Interclassement : `utf8mb4_unicode_ci`
4. Cliquer sur "Créer"

### Étape 3 : Importer le schéma
1. Sélectionner la base `cmc_agriculture`
2. Cliquer sur l'onglet "Importer"
3. Cliquer sur "Choisir un fichier"
4. Sélectionner le fichier `database/schema.sql`
5. Cliquer sur "Exécuter"

### Étape 4 : Vérification
- Vérifier que 10 tables ont été créées
- Vérifier que les données d'exemple sont présentes

## 🖥️ Méthode 2 : Avec MySQL Workbench

### Étape 1 : Ouvrir MySQL Workbench
1. Lancer MySQL Workbench
2. Se connecter à votre serveur MySQL local

### Étape 2 : Créer la base de données
1. Cliquer sur "Create a new schema"
2. Nom : `cmc_agriculture`
3. Charset : `utf8mb4`
4. Cliquer sur "Apply"

### Étape 3 : Exécuter le script
1. Ouvrir un nouvel onglet SQL
2. Copier le contenu de `database/schema.sql`
3. Coller dans l'éditeur SQL
4. Cliquer sur l'icône "Execute" (⚡)

## ⌨️ Méthode 3 : Ligne de commande

### Étape 1 : Se connecter à MySQL
```bash
mysql -u root -p
```

### Étape 2 : Créer la base de données
```sql
CREATE DATABASE cmc_agriculture CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE cmc_agriculture;
```

### Étape 3 : Exécuter le script
```bash
mysql -u root -p cmc_agriculture < database/schema.sql
```

## 📊 Structure de la base de données créée

### Tables principales :
1. **users** - Utilisateurs (admin, étudiants, formateurs)
2. **actualites** - Articles et actualités du site
3. **evenements** - Événements et manifestations
4. **formations** - Programmes de formation
5. **formation_modules** - Modules de chaque formation
6. **emplois_du_temps** - Fichiers d'emplois du temps
7. **inscriptions** - Inscriptions aux formations/événements

### Tables de référence :
8. **categories** - Catégories pour actualités/formations/événements
9. **filieres** - Filières de formation
10. **groupes** - Groupes d'étudiants par filière

### Vues créées :
- **v_formations_completes** - Formations avec catégories et modules
- **v_actualites_completes** - Actualités avec catégories
- **v_evenements_complets** - Événements avec catégories

## 🔧 Configuration pour l'application

### Fichier de configuration (à créer)
Créer un fichier `config/database.php` :

```php
<?php
return [
    'host' => 'localhost',
    'database' => 'cmc_agriculture',
    'username' => 'root',
    'password' => 'votre_mot_de_passe',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];
?>
```

### Variables d'environnement (.env)
```env
DB_HOST=localhost
DB_DATABASE=cmc_agriculture
DB_USERNAME=root
DB_PASSWORD=votre_mot_de_passe
DB_CHARSET=utf8mb4
```

## 📝 Données d'exemple incluses

### Utilisateurs :
- **Admin** : <EMAIL> / password
- **Formateur** : <EMAIL> / password
- **Étudiants** : <EMAIL>, <EMAIL>

### Contenu :
- **3 actualités** (infrastructure, événement, partenariat)
- **3 événements** (portes ouvertes, conférence, atelier)
- **3 formations** complètes avec modules
- **4 emplois du temps** par filière/groupe
- **3 inscriptions** d'exemple

### Catégories :
- **Actualités** : Infrastructure, Événement, Formation, Partenariat, Réussite
- **Formations** : Culture, Élevage, Gestion, Agriculture durable
- **Événements** : Portes ouvertes, Conférence, Atelier, Forum

## 🔍 Vérification de l'installation

### Requêtes de test :
```sql
-- Vérifier les tables
SHOW TABLES;

-- Compter les enregistrements
SELECT 'actualites' as table_name, COUNT(*) as count FROM actualites
UNION ALL
SELECT 'evenements', COUNT(*) FROM evenements
UNION ALL
SELECT 'formations', COUNT(*) FROM formations
UNION ALL
SELECT 'users', COUNT(*) FROM users;

-- Tester les vues
SELECT * FROM v_formations_completes;
SELECT * FROM v_actualites_completes WHERE statut = 'publie';
```

### Résultats attendus :
- 10 tables créées
- 3 actualités, 3 événements, 3 formations
- 4 utilisateurs, 24 modules de formation
- 3 vues fonctionnelles

## 🛠️ Maintenance et sauvegarde

### Sauvegarde régulière :
```bash
mysqldump -u root -p cmc_agriculture > backup_$(date +%Y%m%d).sql
```

### Restauration :
```bash
mysql -u root -p cmc_agriculture < backup_20241215.sql
```

## 🚨 Dépannage

### Problèmes courants :

1. **Erreur de connexion**
   - Vérifier que MySQL est démarré
   - Vérifier les identifiants de connexion

2. **Erreur d'importation**
   - Vérifier l'encodage du fichier (UTF-8)
   - Augmenter la limite d'importation dans phpMyAdmin

3. **Tables non créées**
   - Vérifier les privilèges de l'utilisateur MySQL
   - Exécuter le script par parties

4. **Données manquantes**
   - Vérifier que toutes les INSERT ont été exécutées
   - Contrôler les contraintes de clés étrangères

## ✅ Checklist finale

- [ ] Base de données `cmc_agriculture` créée
- [ ] 10 tables créées avec succès
- [ ] Données d'exemple insérées
- [ ] 3 vues créées et fonctionnelles
- [ ] Configuration de connexion testée
- [ ] Sauvegarde initiale effectuée

## 🎉 Prochaines étapes

Une fois la base de données créée :
1. **Configurer l'application** avec les paramètres de connexion
2. **Tester les fonctionnalités** admin et publiques
3. **Personnaliser les données** selon vos besoins
4. **Mettre en place la sauvegarde** automatique

Votre base de données est maintenant prête pour le site web CMC Agriculture ! 🚀
