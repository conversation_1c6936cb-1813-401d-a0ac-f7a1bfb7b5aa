import { useState } from "react";
import SectionHeader from "@/components/ui/section-header";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const EspaceEtudiant = () => {
  const [selectedFiliere, setSelectedFiliere] = useState("");
  const [selectedGroupe, setSelectedGroupe] = useState("");

  const handleFiliereChange = (value: string) => {
    setSelectedFiliere(value);
    setSelectedGroupe("");
  };

  const filieres = [
    { value: "culture", label: "Culture" },
    { value: "elevage", label: "Élevage" },
    { value: "gestion", label: "Gestion d'exploitation" },
    { value: "durable", label: "Agriculture durable" }
  ];

  const groupes = {
    culture: [
      { value: "c1", label: "Culture - Groupe 1" },
      { value: "c2", label: "Culture - Groupe 2" }
    ],
    elevage: [
      { value: "e1", label: "Élevage - Groupe 1" },
      { value: "e2", label: "Élevage - Groupe 2" }
    ],
    gestion: [
      { value: "g1", label: "Gestion - Groupe 1" }
    ],
    durable: [
      { value: "d1", label: "Agriculture durable - Groupe 1" },
      { value: "d2", label: "Agriculture durable - Groupe 2" }
    ]
  };

  type GroupKey = keyof typeof groupes;

  const selectedGroupes = selectedFiliere ? groupes[selectedFiliere as GroupKey] : [];

  const getScheduleImageUrl = () => {
    if (!selectedFiliere || !selectedGroupe) return "";
    return `/images/emplois-du-temps/${selectedFiliere}-${selectedGroupe}.png`;
  };

  return (
    <div>
      <section className="bg-agro-700 text-white py-16 md:py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Espace Stagiaire</h1>
            <p className="text-xl mb-0">
              Consultez l'emploi du temps de votre formation.
            </p>
          </div>
        </div>
      </section>

      <section className="py-8 container mx-auto px-6">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Sélectionnez votre filière et groupe</CardTitle>
              <CardDescription>
                Veuillez choisir votre filière de formation et votre groupe pour accéder à votre emploi du temps.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="filiere">Filière</Label>
                <Select value={selectedFiliere} onValueChange={handleFiliereChange}>
                  <SelectTrigger id="filiere">
                    <SelectValue placeholder="Sélectionnez votre filière" />
                  </SelectTrigger>
                  <SelectContent>
                    {filieres.map((filiere) => (
                      <SelectItem key={filiere.value} value={filiere.value}>
                        {filiere.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {selectedFiliere && (
                <div className="space-y-2">
                  <Label htmlFor="groupe">Groupe</Label>
                  <Select 
                    value={selectedGroupe} 
                    onValueChange={setSelectedGroupe}
                  >
                    <SelectTrigger id="groupe">
                      <SelectValue placeholder="Sélectionnez votre groupe" />
                    </SelectTrigger>
                    <SelectContent>
                      {selectedGroupes.map((groupe) => (
                        <SelectItem key={groupe.value} value={groupe.value}>
                          {groupe.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </CardContent>
          </Card>

          {selectedFiliere && selectedGroupe && (
            <div className="mt-8">
              <Card>
                <CardHeader>
                  <CardTitle>Emploi du temps</CardTitle>
                  <CardDescription>
                    Voici l'emploi du temps pour votre groupe. Cliquez sur l'image pour la télécharger.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <a 
                    href={getScheduleImageUrl()} 
                    download 
                    className="block w-full hover:opacity-90 transition-opacity"
                  >
                    <img 
                      src={getScheduleImageUrl()} 
                      alt={`Emploi du temps ${selectedFiliere} - ${selectedGroupe}`} 
                      className="w-full rounded-lg shadow-lg"
                    />
                  </a>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default EspaceEtudiant;
