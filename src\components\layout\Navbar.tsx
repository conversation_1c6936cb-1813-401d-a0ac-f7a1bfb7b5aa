
import { NavLink } from "react-router-dom";
import { Menu, X, ChevronDown } from "lucide-react";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";

const Navbar = () => {
  const [isNavOpen, setIsNavOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };
    
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const toggleNav = () => {
    setIsNavOpen(!isNavOpen);
  };

  const closeNav = () => {
    setIsNavOpen(false);
  };

  const navLinks = [
    { name: "Accueil", path: "/" },
    { name: "Présentation", path: "/presentation" },
    { name: "Formations", path: "/formations" },
    { name: "Actualités", path: "/actualites" },
    { name: "Espace Stagiaire", path: "/espace-etudiant" },
  ];

  return (
    <header 
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-500",
        scrolled 
          ? "bg-white/90 backdrop-blur-md shadow-lg border-b border-agro-200" 
          : "bg-white"
      )}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20 md:h-24">
          <div className="flex-shrink-0 flex items-center">
            <NavLink to="/" className="flex items-center group relative" onClick={closeNav}>
              <div className="logo-container relative p-3 rounded-xl bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-sm border border-white/30 shadow-xl group-hover:shadow-2xl transition-all duration-500 group-hover:scale-105 hover:border-agro-300/50">
                <img
                  src="/lovable-uploads/89dd2013-01a7-491c-9930-53169b02cd36.png"
                  alt="Logo CMC"
                  className="logo-glow max-h-16 md:max-h-20 w-auto object-contain transition-all duration-500 group-hover:brightness-110"
                />
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-agro-400/20 to-agro-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute -inset-1 bg-gradient-to-r from-agro-400 to-agro-600 rounded-xl blur opacity-0 group-hover:opacity-20 transition-opacity duration-500"></div>
              </div>
            </NavLink>
          </div>

          {/* Desktop navigation */}
          <NavigationMenu className="hidden md:flex">
            <NavigationMenuList className="gap-0.5">
              {navLinks.map((link) => (
                <NavigationMenuItem key={link.name}>
                  <NavigationMenuLink
                    asChild
                    className={cn(
                      "group block relative py-2 px-3 text-sm font-medium rounded-md transition-colors",
                      "hover:bg-agro-50 hover:text-agro-700"
                    )}
                  >
                    <NavLink to={link.path} end={link.path === "/"} className={({ isActive }) => 
                      isActive ? "text-agro-700 bg-agro-50" : "text-slate-800"
                    }>
                      <span className="relative z-10">{link.name}</span>
                      <span className="absolute bottom-0 left-1/2 -translate-x-1/2 w-1/2 h-0.5 bg-agro-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
                    </NavLink>
                  </NavigationMenuLink>
                </NavigationMenuItem>
              ))}
            </NavigationMenuList>
          </NavigationMenu>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleNav}
              aria-label="Toggle navigation menu"
              className="relative"
            >
              <div className="relative w-6 h-6">
                <span 
                  className={cn(
                    "absolute block bg-agro-800 transform transition-all duration-300",
                    "h-0.5 w-6 rounded-sm",
                    isNavOpen ? "top-3 rotate-45" : "top-1"
                  )}
                />
                <span 
                  className={cn(
                    "absolute block bg-agro-800 top-3 h-0.5 w-6 rounded-sm",
                    "transform transition-all duration-300",
                    isNavOpen ? "opacity-0" : "opacity-100"
                  )}
                />
                <span 
                  className={cn(
                    "absolute block bg-agro-800 transform transition-all duration-300",
                    "h-0.5 w-6 rounded-sm",
                    isNavOpen ? "bottom-2.5 -rotate-45" : "bottom-1"
                  )}
                />
              </div>
            </Button>
          </div>
        </div>

        {/* Mobile navigation */}
        <div 
          className={cn(
            "md:hidden overflow-hidden transition-all duration-300 ease-in-out",
            isNavOpen ? "max-h-96 border-t border-agro-100" : "max-h-0"
          )}
        >
          <nav className="py-3 space-y-1">
            {navLinks.map((link) => (
              <NavLink
                key={link.name}
                to={link.path}
                className={({ isActive }) =>
                  cn(
                    "block py-2.5 px-4 rounded-lg transition-all duration-300 relative overflow-hidden", 
                    isActive 
                      ? "bg-agro-100 text-agro-800 font-medium" 
                      : "hover:bg-agro-50 hover:text-agro-700 text-slate-700"
                  )
                }
                onClick={closeNav}
                end={link.path === "/"}
              >
                <div className="relative z-10 flex items-center justify-between">
                  <span>{link.name}</span>
                  <ChevronDown className={cn(
                    "h-4 w-4 transition-transform duration-300",
                    isNavOpen ? "rotate-180" : "rotate-0"
                  )} />
                </div>
              </NavLink>
            ))}
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
