import { useState } from "react";
import SectionHeader from "@/components/ui/section-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { Clock, Calendar, BookOpen, User, Users, Award, Filter, Search, Sprout, UserCheck, ChevronRight, Phone, Mail, MapPin } from "lucide-react";

const Formations = () => {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");

  const formations = [
    {
      id: 1,
      title: "Technicien en Production Végétale",
      category: "culture",
      badge: "Populaire",
      level: "Technicien",
      duration: "24 mois",
      description: "Formation complète en production végétale couvrant les cultures maraîchères, céréalières et arboricoles avec les techniques modernes de production.",
      modules: [
        "Agronomie générale et pédologie",
        "Production des cultures maraîchères",
        "Production des cultures céréalières",
        "Arboriculture fruitière",
        "Protection phytosanitaire",
        "Irrigation et fertilisation",
        "Mécanisation agricole",
        "Post-récolte et conditionnement"
      ],
      prerequisites: "Niveau baccalauréat ou équivalent",
      outcomes: "Technicien spécialisé en production végétale capable de gérer une exploitation agricole moderne",
      startDate: "Septembre 2025",
      image: "https://images.unsplash.com/photo-1500651230702-0e2d8a49d4ad?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 2,
      title: "Technicien Spécialisé en Élevage",
      category: "elevage",
      badge: "Nouvelle session",
      level: "Technicien Spécialisé",
      duration: "30 mois",
      description: "Formation avancée en élevage couvrant tous les aspects de la production animale : bovins, ovins, caprins, aviculture et apiculture.",
      modules: [
        "Zootechnie générale",
        "Nutrition et alimentation animale",
        "Reproduction et amélioration génétique",
        "Pathologie et prophylaxie",
        "Gestion technico-économique d'élevage",
        "Transformation des produits animaux",
        "Commercialisation des produits d'élevage",
        "Législation et réglementation"
      ],
      prerequisites: "Niveau baccalauréat scientifique ou technique",
      outcomes: "Technicien spécialisé capable de diriger une exploitation d'élevage et d'optimiser la production",
      startDate: "Septembre 2025",
      image: "https://images.unsplash.com/photo-1596473185237-3d5768653bd8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 3,
      title: "Gestion d'exploitation agricole",
      category: "gestion",
      level: "Technicien spécialisé",
      duration: "18 mois",
      description: "Formation complète sur la gestion technique, financière et administrative d'une exploitation agricole.",
      modules: [
        "Principes de gestion d'entreprise",
        "Comptabilité agricole",
        "Marketing des produits agricoles",
        "Planification et stratégie",
        "Gestion des ressources humaines",
        "Réglementation et normes agricoles"
      ],
      prerequisites: "Niveau baccalauréat avec expérience agricole ou formation technique agricole",
      outcomes: "Gestionnaire d'exploitation agricole capable de diriger et développer une entreprise agricole",
      startDate: "Octobre 2025",
      image: "https://images.unsplash.com/photo-1598512752271-33f913a5af13?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 4,
      title: "Technicien en Mécanisation Agricole",
      category: "mecanique",
      badge: "Haute demande",
      level: "Technicien",
      duration: "24 mois",
      description: "Formation spécialisée en mécanisation agricole, maintenance et conduite des équipements agricoles modernes.",
      modules: [
        "Mécanique générale et hydraulique",
        "Moteurs et systèmes de transmission",
        "Équipements de travail du sol",
        "Machines de semis et plantation",
        "Équipements de récolte",
        "Maintenance préventive et curative",
        "Gestion d'un parc matériel",
        "Sécurité et réglementation"
      ],
      prerequisites: "Niveau baccalauréat ou expérience dans le secteur agricole",
      outcomes: "Spécialiste en agriculture durable capable d'implémenter et gérer des systèmes agricoles écologiques",
      startDate: "Octobre 2025",
      image: "https://images.unsplash.com/photo-1464226184884-fa280b87c399?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 5,
      title: "Mécanisation agricole",
      category: "mecanisation",
      level: "Technicien",
      duration: "12 mois",
      description: "Formation sur l'utilisation, l'entretien et la gestion des machines et équipements agricoles.",
      modules: [
        "Principes de mécanique agricole",
        "Utilisation des tracteurs et équipements",
        "Entretien et réparation",
        "Technologies d'agriculture de précision",
        "Gestion du parc matériel",
        "Sécurité et réglementation"
      ],
      prerequisites: "Niveau baccalauréat technique ou scientifique",
      outcomes: "Technicien en mécanisation agricole capable de gérer et optimiser l'utilisation des machines agricoles",
      startDate: "Septembre 2025",
      image: "https://images.unsplash.com/photo-1591086429611-387826962b8e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 6,
      title: "Transformation des produits agricoles",
      category: "transformation",
      level: "Technicien",
      duration: "12 mois",
      description: "Formation sur les techniques de transformation, conservation et valorisation des produits agricoles.",
      modules: [
        "Principes de transformation alimentaire",
        "Techniques de conservation",
        "Contrôle qualité et sécurité alimentaire",
        "Développement de produits",
        "Emballage et conditionnement",
        "Réglementation alimentaire"
      ],
      prerequisites: "Niveau baccalauréat",
      outcomes: "Technicien en transformation alimentaire capable de gérer des processus de transformation de produits agricoles",
      startDate: "Octobre 2025",
      image: "https://images.unsplash.com/photo-1563865436914-44ee14a35e4b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    }
  ];

  const filteredFormations = formations.filter(formation => {
    const matchesSearch = formation.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          formation.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (categoryFilter === "all") {
      return matchesSearch;
    } else {
      return matchesSearch && formation.category === categoryFilter;
    }
  });

  const categories = [
    { value: "all", label: "Toutes les catégories" },
    { value: "culture", label: "Culture" },
    { value: "elevage", label: "Élevage" },
    { value: "gestion", label: "Gestion" },
    { value: "durable", label: "Agriculture durable" },
    { value: "mecanisation", label: "Mécanisation" },
    { value: "transformation", label: "Transformation" }
  ];

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Demande d'inscription envoyée",
      description: "Nous avons bien reçu votre demande d'inscription. Notre équipe vous contactera prochainement.",
    });
  };

  return (
    <div>
      <section className="bg-agro-700 text-white py-16 md:py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Nos formations</h1>
            <p className="text-xl mb-0">
              Découvrez nos programmes de formation professionnelle dans le domaine agricole, conçus pour répondre aux besoins du marché.
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 container mx-auto px-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6 mb-10">
          <SectionHeader 
            title="Formations disponibles" 
            subtitle="Des programmes adaptés à tous les profils et tous les projets professionnels"
            className="mb-0"
          />
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Rechercher une formation..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <div className="flex items-center">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Catégorie" />
                </div>
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredFormations.map((formation) => (
            <Card key={formation.id} className="overflow-hidden card-hover">
              <div className="h-48 overflow-hidden">
                <img 
                  src={formation.image} 
                  alt={formation.title} 
                  className="w-full h-full object-cover"
                />
              </div>
              <CardHeader className="pb-4">
                <div className="flex justify-between items-start mb-2">
                  <Badge variant="outline" className="bg-agro-50 text-agro-700">
                    {categories.find(c => c.value === formation.category)?.label}
                  </Badge>
                  {formation.badge && (
                    <Badge className="bg-agro-600">{formation.badge}</Badge>
                  )}
                </div>
                <CardTitle>{formation.title}</CardTitle>
                <CardDescription className="text-base">
                  {formation.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <Award className="mr-2 h-4 w-4 text-agro-600" />
                    <span className="text-sm">{formation.level}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-agro-600" />
                    <span className="text-sm">{formation.duration}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4 text-agro-600" />
                    <span className="text-sm">{formation.startDate}</span>
                  </div>
                  <div className="flex items-center">
                    <BookOpen className="mr-2 h-4 w-4 text-agro-600" />
                    <span className="text-sm">{formation.modules.length} modules</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="w-full">Détails et inscription</Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>{formation.title}</DialogTitle>
                      <DialogDescription>
                        Niveau: {formation.level} | Durée: {formation.duration}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 py-4">
                      <div className="lg:col-span-2">
                        <div className="mb-6">
                          <h3 className="text-lg font-semibold mb-2">Description</h3>
                          <p>{formation.description}</p>
                        </div>
                        
                        <div className="mb-6">
                          <h3 className="text-lg font-semibold mb-2">Modules de formation</h3>
                          <ul className="space-y-2">
                            {formation.modules.map((module, index) => (
                              <li key={index} className="flex items-start">
                                <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                                <span>{module}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                          <div>
                            <h3 className="text-lg font-semibold mb-2">Prérequis</h3>
                            <p>{formation.prerequisites}</p>
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold mb-2">Débouchés</h3>
                            <p>{formation.outcomes}</p>
                          </div>
                        </div>
                        
                        <div>
                          <h3 className="text-lg font-semibold mb-2">Informations pratiques</h3>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="flex items-center">
                              <Calendar className="mr-2 h-5 w-5 text-agro-600" />
                              <span>Début: {formation.startDate}</span>
                            </div>
                            <div className="flex items-center">
                              <Clock className="mr-2 h-5 w-5 text-agro-600" />
                              <span>Durée: {formation.duration}</span>
                            </div>
                            <div className="flex items-center">
                              <Users className="mr-2 h-5 w-5 text-agro-600" />
                              <span>Places limitées: 25</span>
                            </div>
                            <div className="flex items-center">
                              <Award className="mr-2 h-5 w-5 text-agro-600" />
                              <span>Certification officielle</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <Card>
                          <CardHeader>
                            <CardTitle>Formulaire d'inscription</CardTitle>
                            <CardDescription>
                              Remplissez ce formulaire pour vous inscrire à cette formation
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <form onSubmit={handleFormSubmit} className="space-y-4">
                              <div className="space-y-2">
                                <Label htmlFor="name">Nom complet</Label>
                                <Input id="name" placeholder="Votre nom et prénom" required />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="email">Email</Label>
                                <Input id="email" type="email" placeholder="<EMAIL>" required />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="phone">Téléphone</Label>
                                <Input id="phone" placeholder="06 XX XX XX XX" required />
                              </div>
                              <div className="space-y-2">
                                <Label>Niveau d'études</Label>
                                <RadioGroup defaultValue="bac">
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem value="bac" id="bac" />
                                    <Label htmlFor="bac">Baccalauréat</Label>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem value="bac+2" id="bac+2" />
                                    <Label htmlFor="bac+2">Bac+2</Label>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem value="higher" id="higher" />
                                    <Label htmlFor="higher">Supérieur à Bac+2</Label>
                                  </div>
                                </RadioGroup>
                              </div>
                              <Button type="submit" className="w-full">Envoyer ma demande</Button>
                            </form>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        {filteredFormations.length === 0 && (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">Aucune formation ne correspond à votre recherche.</p>
            <Button variant="link" onClick={() => {setSearchTerm(""); setCategoryFilter("all");}}>
              Réinitialiser les filtres
            </Button>
          </div>
        )}
      </section>

      <section className="py-16 bg-agro-50">
        <div className="container mx-auto px-6">
          <SectionHeader 
            title="Informations sur les formations" 
            subtitle="Tout ce que vous devez savoir sur nos programmes de formation"
            center
          />
          
          <Tabs defaultValue="admission" className="max-w-4xl mx-auto">
            <TabsList className="grid w-full grid-cols-1 md:grid-cols-3">
              <TabsTrigger value="admission">Conditions d'admission</TabsTrigger>
              <TabsTrigger value="calendar">Calendrier des formations</TabsTrigger>
              <TabsTrigger value="faq">Questions fréquentes</TabsTrigger>
            </TabsList>
            <TabsContent value="admission" className="mt-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold mb-3 flex items-center">
                        <User className="mr-2 h-5 w-5 text-agro-600" />
                        Critères d'admissibilité
                      </h3>
                      <ul className="space-y-2">
                        <li className="flex items-start">
                          <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Être âgé de 18 à 30 ans</span>
                        </li>
                        <li className="flex items-start">
                          <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Être titulaire d'un baccalauréat ou équivalent (pour les formations de technicien)</span>
                        </li>
                        <li className="flex items-start">
                          <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Avoir validé un niveau technicien ou équivalent (pour les formations de technicien spécialisé)</span>
                        </li>
                        <li className="flex items-start">
                          <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Réussir l'entretien de sélection</span>
                        </li>
                      </ul>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <h3 className="text-xl font-semibold mb-3 flex items-center">
                        <Award className="mr-2 h-5 w-5 text-agro-600" />
                        Processus de sélection
                      </h3>
                      <ol className="space-y-4">
                        <li className="flex">
                          <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-agro-100 text-agro-800 font-semibold mr-3">
                            1
                          </div>
                          <div>
                            <h4 className="font-medium">Dépôt de candidature</h4>
                            <p className="text-muted-foreground">Compléter le formulaire d'inscription en ligne ou sur place</p>
                          </div>
                        </li>
                        <li className="flex">
                          <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-agro-100 text-agro-800 font-semibold mr-3">
                            2
                          </div>
                          <div>
                            <h4 className="font-medium">Étude du dossier</h4>
                            <p className="text-muted-foreground">Vérification des prérequis et de l'adéquation avec la formation</p>
                          </div>
                        </li>
                        <li className="flex">
                          <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-agro-100 text-agro-800 font-semibold mr-3">
                            3
                          </div>
                          <div>
                            <h4 className="font-medium">Entretien de motivation</h4>
                            <p className="text-muted-foreground">Évaluation de la motivation et du projet professionnel du candidat</p>
                          </div>
                        </li>
                        <li className="flex">
                          <div className="flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full bg-agro-100 text-agro-800 font-semibold mr-3">
                            4
                          </div>
                          <div>
                            <h4 className="font-medium">Décision finale</h4>
                            <p className="text-muted-foreground">Notification des résultats et finalisation de l'inscription</p>
                          </div>
                        </li>
                      </ol>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <h3 className="text-xl font-semibold mb-3">
                        <Sprout className="mr-2 h-5 w-5 text-agro-600" />
                        Frais de formation
                      </h3>
                      <p className="mb-4">Les formations au sein du Pôle Agriculture de la CMC sont prises en charge par l'État marocain dans le cadre du plan de développement de la formation professionnelle.</p>
                      <p className="mb-4">Des frais d'inscription annuels de 1500 MAD sont applicables et couvrent :</p>
                      <ul className="space-y-2">
                        <li className="flex items-start">
                          <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Frais administratifs</span>
                        </li>
                        <li className="flex items-start">
                          <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Assurance stagiaire</span>
                        </li>
                        <li className="flex items-start">
                          <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Documentation pédagogique</span>
                        </li>
                        <li className="flex items-start">
                          <ChevronRight className="h-5 w-5 text-agro-600 mr-2 flex-shrink-0 mt-0.5" />
                          <span>Activités parascolaires</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="calendar" className="mt-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold mb-3">Calendrier académique 2025-2026</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card className="bg-agro-50">
                          <CardHeader>
                            <CardTitle className="text-lg">Premier semestre</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-3">
                              <li className="flex justify-between">
                                <span>Début des cours</span>
                                <span className="font-medium">13 septembre 2025</span>
                              </li>
                              <li className="flex justify-between">
                                <span>Examens mi-semestre</span>
                                <span className="font-medium">10-14 novembre 2025</span>
                              </li>
                              <li className="flex justify-between">
                                <span>Vacances d'hiver</span>
                                <span className="font-medium">21 décembre - 5 janvier 2026</span>
                              </li>
                              <li className="flex justify-between">
                                <span>Examens finaux</span>
                                <span className="font-medium">18-29 janvier 2026</span>
                              </li>
                            </ul>
                          </CardContent>
                        </Card>
                        <Card className="bg-agro-50">
                          <CardHeader>
                            <CardTitle className="text-lg">Deuxième semestre</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-3">
                              <li className="flex justify-between">
                                <span>Début des cours</span>
                                <span className="font-medium">8 février 2026</span>
                              </li>
                              <li className="flex justify-between">
                                <span>Examens mi-semestre</span>
                                <span className="font-medium">5-9 avril 2026</span>
                              </li>
                              <li className="flex justify-between">
                                <span>Vacances de printemps</span>
                                <span className="font-medium">18-25 avril 2026</span>
                              </li>
                              <li className="flex justify-between">
                                <span>Examens finaux</span>
                                <span className="font-medium">14-25 juin 2026</span>
                              </li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <h3 className="text-xl font-semibold mb-3">Périodes de stage</h3>
                      <p className="mb-4">Toutes nos formations comprennent des périodes de stage en entreprise, permettant aux stagiaires d'acquérir une expérience pratique.</p>
                      <div className="grid grid-cols-1 gap-4">
                        <div className="bg-agro-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Formations Technicien (12 mois)</h4>
                          <ul className="space-y-2">
                            <li className="flex justify-between">
                              <span>Stage d'observation</span>
                              <span className="font-medium">2 semaines (novembre 2025)</span>
                            </li>
                            <li className="flex justify-between">
                              <span>Stage d'application</span>
                              <span className="font-medium">6 semaines (mai-juin 2026)</span>
                            </li>
                          </ul>
                        </div>
                        <div className="bg-agro-50 p-4 rounded-lg">
                          <h4 className="font-semibold mb-2">Formations Technicien Spécialisé (18 mois)</h4>
                          <ul className="space-y-2">
                            <li className="flex justify-between">
                              <span>Stage d'observation</span>
                              <span className="font-medium">2 semaines (novembre 2025)</span>
                            </li>
                            <li className="flex justify-between">
                              <span>Stage d'application</span>
                              <span className="font-medium">8 semaines (juillet-août 2026)</span>
                            </li>
                            <li className="flex justify-between">
                              <span>Stage de fin d'études</span>
                              <span className="font-medium">12 semaines (mars-mai 2027)</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div>
                      <h3 className="text-xl font-semibold mb-3">Sessions d'admission</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-lg">Session principale</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-3">
                              <li className="flex justify-between">
                                <span>Dépôt des dossiers</span>
                                <span className="font-medium">15 juin - 31 juillet 2025</span>
                              </li>
                              <li className="flex justify-between">
                                <span>Entretiens</span>
                                <span className="font-medium">10-25 août 2025</span>
                              </li>
                              <li className="flex justify-between">
                                <span>Résultats</span>
                                <span className="font-medium">31 août 2025</span>
                              </li>
                            </ul>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-lg">Session complémentaire</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-3">
                              <li className="flex justify-between">
                                <span>Dépôt des dossiers</span>
                                <span className="font-medium">1-15 septembre 2025</span>
                              </li>
                              <li className="flex justify-between">
                                <span>Entretiens</span>
                                <span className="font-medium">16-20 septembre 2025</span>
                              </li>
                              <li className="flex justify-between">
                                <span>Résultats</span>
                                <span className="font-medium">22 septembre 2025</span>
                              </li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="faq" className="mt-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    <div>
                      <h4 className="text-lg font-semibold mb-2">Est-ce que je peux m'inscrire si j'ai plus de 30 ans ?</h4>
                      <p className="text-muted-foreground">
                        Des exceptions peuvent être accordées sur dossier pour les candidats de plus de 30 ans, notamment s'ils disposent d'une expérience pertinente dans le secteur ou d'un projet professionnel solide.
                      </p>
                    </div>
                    <Separator />
                    <div>
                      <h4 className="text-lg font-semibold mb-2">Les formations sont-elles accessibles aux étrangers ?</h4>
                      <p className="text-muted-foreground">
                        Oui, les formations sont accessibles aux étudiants étrangers sous réserve d'avoir les documents de séjour appropriés et de maîtriser la langue française.
                      </p>
                    </div>
                    <Separator />
                    <div>
                      <h4 className="text-lg font-semibold mb-2">Y a-t-il des bourses disponibles ?</h4>
                      <p className="text-muted-foreground">
                        Des bourses d'étude peuvent être accordées aux stagiaires méritants, en fonction de critères académiques et sociaux. Les dossiers sont à soumettre au début de l'année académique.
                      </p>
                    </div>
                    <Separator />
                    <div>
                      <h4 className="text-lg font-semibold mb-2">Comment se déroule la formation pratique ?</h4>
                      <p className="text-muted-foreground">
                        La formation pratique se déroule dans nos ateliers et notre ferme pédagogique, ainsi que lors des stages en entreprise. Les stagiaires passent environ 60% de leur temps en formation pratique.
                      </p>
                    </div>
                    <Separator />
                    <div>
                      <h4 className="text-lg font-semibold mb-2">Quel est le taux d'insertion professionnelle après la formation ?</h4>
                      <p className="text-muted-foreground">
                        Le taux d'insertion professionnelle à 6 mois après l'obtention du diplôme est d'environ 80%. Nous disposons d'un service dédié à l'accompagnement vers l'emploi ou l'entrepreneuriat.
                      </p>
                    </div>
                    <Separator />
                    <div>
                      <h4 className="text-lg font-semibold mb-2">Puis-je poursuivre mes études après avoir obtenu mon diplôme ?</h4>
                      <p className="text-muted-foreground">
                        Oui, les diplômes délivrés par la CMC permettent de poursuivre des études supérieures dans des établissements nationaux ou internationaux, selon les passerelles existantes.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      <section className="py-16 bg-agro-700 text-white">
        <div className="container mx-auto px-6 max-w-4xl text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Besoin d'informations supplémentaires ?</h2>
          <p className="text-lg mb-8">
            Notre équipe est à votre disposition pour répondre à toutes vos questions concernant nos formations et le processus d'admission.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-white/10 border-white/20">
              <CardContent className="pt-6 flex flex-col items-center">
                <div className="rounded-full bg-white/20 p-3 mb-4">
                  <Phone className="h-6 w-6" />
                </div>
                <h3 className="font-semibold mb-2">Par téléphone</h3>
                <p className="text-white/80 text-center">+212 5XX XX XX XX</p>
                <p className="text-white/60 text-sm text-center">Lun-Ven, 9h-17h</p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 border-white/20">
              <CardContent className="pt-6 flex flex-col items-center">
                <div className="rounded-full bg-white/20 p-3 mb-4">
                  <Mail className="h-6 w-6" />
                </div>
                <h3 className="font-semibold mb-2">Par email</h3>
                <p className="text-white/80 text-center"><EMAIL></p>
                <p className="text-white/60 text-sm text-center">Réponse sous 48h</p>
              </CardContent>
            </Card>
            <Card className="bg-white/10 border-white/20">
              <CardContent className="pt-6 flex flex-col items-center">
                <div className="rounded-full bg-white/20 p-3 mb-4">
                  <MapPin className="h-6 w-6" />
                </div>
                <h3 className="font-semibold mb-2">Sur place</h3>
                <p className="text-white/80 text-center">123 Avenue de l'Agriculture</p>
                <p className="text-white/60 text-sm text-center">Lun-Ven, 9h-17h</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Formations;
