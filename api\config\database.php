<?php
// Configuration de la base de données
class Database {
    private $host = 'localhost';
    private $db_name = 'cmc_agriculture';
    private $username = 'root';
    private $password = ''; // Remplacez par votre mot de passe MySQL
    private $conn;

    // Connexion à la base de données
    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "Erreur de connexion: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}
?>
