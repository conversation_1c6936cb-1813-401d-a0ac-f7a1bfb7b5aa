<?php
// API pour récupérer les actualités
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'GET':
        // Récupérer toutes les actualités publiées
        $query = "SELECT a.*, c.nom as categorie_nom, c.code as categorie_code 
                  FROM actualites a 
                  LEFT JOIN categories c ON a.category_id = c.id 
                  WHERE a.statut = 'publie' 
                  ORDER BY a.date_publication DESC";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        $actualites = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'status' => 'success',
            'data' => $actualites,
            'count' => count($actualites)
        ]);
        break;
        
    case 'POST':
        // Ajouter une nouvelle actualité (pour l'admin)
        $data = json_decode(file_get_contents("php://input"), true);
        
        $query = "INSERT INTO actualites (titre, resume, contenu, category_id, statut, auteur, temps_lecture, image_url, date_publication) 
                  VALUES (:titre, :resume, :contenu, :category_id, :statut, :auteur, :temps_lecture, :image_url, :date_publication)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':titre', $data['titre']);
        $stmt->bindParam(':resume', $data['resume']);
        $stmt->bindParam(':contenu', $data['contenu']);
        $stmt->bindParam(':category_id', $data['category_id']);
        $stmt->bindParam(':statut', $data['statut']);
        $stmt->bindParam(':auteur', $data['auteur']);
        $stmt->bindParam(':temps_lecture', $data['temps_lecture']);
        $stmt->bindParam(':image_url', $data['image_url']);
        $stmt->bindParam(':date_publication', $data['date_publication']);
        
        if($stmt->execute()) {
            echo json_encode([
                'status' => 'success',
                'message' => 'Actualité ajoutée avec succès',
                'id' => $db->lastInsertId()
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'ajout de l\'actualité'
            ]);
        }
        break;
        
    default:
        echo json_encode([
            'status' => 'error',
            'message' => 'Méthode non autorisée'
        ]);
        break;
}
?>
