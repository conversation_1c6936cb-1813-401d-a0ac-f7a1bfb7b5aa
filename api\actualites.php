<?php
// API pour récupérer les actualités
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'GET':
        // Récupérer toutes les actualités publiées
        $query = "SELECT a.*, c.nom as categorie_nom, c.code as categorie_code 
                  FROM actualites a 
                  LEFT JOIN categories c ON a.category_id = c.id 
                  WHERE a.statut = 'publie' 
                  ORDER BY a.date_publication DESC";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        $actualites = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'status' => 'success',
            'data' => $actualites,
            'count' => count($actualites)
        ]);
        break;
        
    case 'POST':
        // Ajouter une nouvelle actualité (pour l'admin)
        $data = json_decode(file_get_contents("php://input"), true);

        // Trouver l'ID de la catégorie
        $category_id = null;
        if (isset($data['category'])) {
            $cat_query = "SELECT id FROM categories WHERE code = :category AND type = 'actualite'";
            $cat_stmt = $db->prepare($cat_query);
            $cat_stmt->bindParam(':category', $data['category']);
            $cat_stmt->execute();
            $category = $cat_stmt->fetch();
            if ($category) {
                $category_id = $category['id'];
            }
        }

        $query = "INSERT INTO actualites (titre, resume, contenu, category_id, statut, auteur, temps_lecture, image_url, date_publication)
                  VALUES (:titre, :resume, :contenu, :category_id, :statut, :auteur, :temps_lecture, :image_url, :date_publication)";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':titre', $data['title']);
        $stmt->bindParam(':resume', $data['excerpt']);
        $stmt->bindParam(':contenu', $data['content']);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':statut', $data['status']);
        $stmt->bindParam(':auteur', $data['author']);
        $stmt->bindParam(':temps_lecture', $data['readTime']);
        $stmt->bindParam(':image_url', $data['image']);
        $stmt->bindParam(':date_publication', $data['date'] ?? date('Y-m-d'));

        if($stmt->execute()) {
            echo json_encode([
                'status' => 'success',
                'message' => 'Actualité ajoutée avec succès',
                'id' => $db->lastInsertId()
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'ajout de l\'actualité'
            ]);
        }
        break;

    case 'PUT':
        // Modifier une actualité
        $data = json_decode(file_get_contents("php://input"), true);
        $id = $_GET['id'] ?? null;

        if (!$id) {
            echo json_encode(['status' => 'error', 'message' => 'ID manquant']);
            break;
        }

        // Trouver l'ID de la catégorie
        $category_id = null;
        if (isset($data['category'])) {
            $cat_query = "SELECT id FROM categories WHERE code = :category AND type = 'actualite'";
            $cat_stmt = $db->prepare($cat_query);
            $cat_stmt->bindParam(':category', $data['category']);
            $cat_stmt->execute();
            $category = $cat_stmt->fetch();
            if ($category) {
                $category_id = $category['id'];
            }
        }

        $query = "UPDATE actualites SET titre = :titre, resume = :resume, contenu = :contenu,
                  category_id = :category_id, statut = :statut, auteur = :auteur,
                  temps_lecture = :temps_lecture, image_url = :image_url
                  WHERE id = :id";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':titre', $data['title']);
        $stmt->bindParam(':resume', $data['excerpt']);
        $stmt->bindParam(':contenu', $data['content']);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':statut', $data['status']);
        $stmt->bindParam(':auteur', $data['author']);
        $stmt->bindParam(':temps_lecture', $data['readTime']);
        $stmt->bindParam(':image_url', $data['image']);
        $stmt->bindParam(':id', $id);

        if($stmt->execute()) {
            echo json_encode(['status' => 'success', 'message' => 'Actualité modifiée avec succès']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Erreur lors de la modification']);
        }
        break;

    case 'DELETE':
        // Supprimer une actualité
        $id = $_GET['id'] ?? null;

        if (!$id) {
            echo json_encode(['status' => 'error', 'message' => 'ID manquant']);
            break;
        }

        $query = "DELETE FROM actualites WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);

        if($stmt->execute()) {
            echo json_encode(['status' => 'success', 'message' => 'Actualité supprimée avec succès']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Erreur lors de la suppression']);
        }
        break;
        
    default:
        echo json_encode([
            'status' => 'error',
            'message' => 'Méthode non autorisée'
        ]);
        break;
}
?>
