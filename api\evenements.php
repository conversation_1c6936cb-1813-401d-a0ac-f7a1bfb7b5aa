<?php
// API pour récupérer les événements
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'GET':
        // Récupérer tous les événements
        $query = "SELECT e.*, c.code as category_code, c.nom as category_nom
                  FROM evenements e 
                  LEFT JOIN categories c ON e.category_id = c.id 
                  ORDER BY e.date_evenement DESC";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        $evenements = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Formater les données pour correspondre au format attendu
        foreach ($evenements as &$evenement) {
            $evenement['date'] = $evenement['date_evenement'];
            $evenement['time'] = ($evenement['heure_debut'] && $evenement['heure_fin']) 
                ? substr($evenement['heure_debut'], 0, 5) . '-' . substr($evenement['heure_fin'], 0, 5)
                : '';
            $evenement['location'] = $evenement['lieu'];
            $evenement['category'] = $evenement['category_code'];
        }
        
        echo json_encode([
            'status' => 'success',
            'data' => $evenements,
            'count' => count($evenements)
        ]);
        break;
        
    case 'POST':
        // Ajouter un nouvel événement
        $data = json_decode(file_get_contents("php://input"), true);
        
        // Trouver l'ID de la catégorie
        $category_id = null;
        if (isset($data['category'])) {
            $cat_query = "SELECT id FROM categories WHERE code = :category AND type = 'evenement'";
            $cat_stmt = $db->prepare($cat_query);
            $cat_stmt->bindParam(':category', $data['category']);
            $cat_stmt->execute();
            $category = $cat_stmt->fetch();
            if ($category) {
                $category_id = $category['id'];
            }
        }
        
        // Parser l'heure (format "9h-17h" -> "09:00:00" et "17:00:00")
        $heure_debut = null;
        $heure_fin = null;
        if (isset($data['time']) && $data['time']) {
            $time_parts = explode('-', $data['time']);
            if (count($time_parts) == 2) {
                $heure_debut = str_replace('h', ':00:00', trim($time_parts[0]));
                $heure_fin = str_replace('h', ':00:00', trim($time_parts[1]));
                
                // Ajouter un 0 si nécessaire (9:00:00 -> 09:00:00)
                if (strlen($heure_debut) == 7) $heure_debut = '0' . $heure_debut;
                if (strlen($heure_fin) == 7) $heure_fin = '0' . $heure_fin;
            }
        }
        
        $query = "INSERT INTO evenements (titre, description, date_evenement, heure_debut, heure_fin, 
                  lieu, category_id, statut, image_url) 
                  VALUES (:titre, :description, :date_evenement, :heure_debut, :heure_fin, 
                  :lieu, :category_id, :statut, :image_url)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':titre', $data['title']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':date_evenement', $data['date']);
        $stmt->bindParam(':heure_debut', $heure_debut);
        $stmt->bindParam(':heure_fin', $heure_fin);
        $stmt->bindParam(':lieu', $data['location']);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':statut', $data['status']);
        $stmt->bindParam(':image_url', $data['image']);
        
        if($stmt->execute()) {
            echo json_encode([
                'status' => 'success',
                'message' => 'Événement ajouté avec succès',
                'id' => $db->lastInsertId()
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'ajout de l\'événement'
            ]);
        }
        break;
        
    case 'PUT':
        // Modifier un événement
        $data = json_decode(file_get_contents("php://input"), true);
        $id = $_GET['id'] ?? null;
        
        if (!$id) {
            echo json_encode(['status' => 'error', 'message' => 'ID manquant']);
            break;
        }
        
        // Trouver l'ID de la catégorie
        $category_id = null;
        if (isset($data['category'])) {
            $cat_query = "SELECT id FROM categories WHERE code = :category AND type = 'evenement'";
            $cat_stmt = $db->prepare($cat_query);
            $cat_stmt->bindParam(':category', $data['category']);
            $cat_stmt->execute();
            $category = $cat_stmt->fetch();
            if ($category) {
                $category_id = $category['id'];
            }
        }
        
        // Parser l'heure
        $heure_debut = null;
        $heure_fin = null;
        if (isset($data['time']) && $data['time']) {
            $time_parts = explode('-', $data['time']);
            if (count($time_parts) == 2) {
                $heure_debut = str_replace('h', ':00:00', trim($time_parts[0]));
                $heure_fin = str_replace('h', ':00:00', trim($time_parts[1]));
                
                if (strlen($heure_debut) == 7) $heure_debut = '0' . $heure_debut;
                if (strlen($heure_fin) == 7) $heure_fin = '0' . $heure_fin;
            }
        }
        
        $query = "UPDATE evenements SET titre = :titre, description = :description, 
                  date_evenement = :date_evenement, heure_debut = :heure_debut, heure_fin = :heure_fin,
                  lieu = :lieu, category_id = :category_id, statut = :statut, image_url = :image_url 
                  WHERE id = :id";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':titre', $data['title']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':date_evenement', $data['date']);
        $stmt->bindParam(':heure_debut', $heure_debut);
        $stmt->bindParam(':heure_fin', $heure_fin);
        $stmt->bindParam(':lieu', $data['location']);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':statut', $data['status']);
        $stmt->bindParam(':image_url', $data['image']);
        $stmt->bindParam(':id', $id);
        
        if($stmt->execute()) {
            echo json_encode(['status' => 'success', 'message' => 'Événement modifié avec succès']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Erreur lors de la modification']);
        }
        break;
        
    case 'DELETE':
        // Supprimer un événement
        $id = $_GET['id'] ?? null;
        
        if (!$id) {
            echo json_encode(['status' => 'error', 'message' => 'ID manquant']);
            break;
        }
        
        $query = "DELETE FROM evenements WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);
        
        if($stmt->execute()) {
            echo json_encode(['status' => 'success', 'message' => 'Événement supprimé avec succès']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Erreur lors de la suppression']);
        }
        break;
        
    default:
        echo json_encode([
            'status' => 'error',
            'message' => 'Méthode non autorisée'
        ]);
        break;
}
?>
