# Guide de test - Connexion base de données

## 🎯 Comment vérifier si la base de données est liée au site

### Étape 1 : Prérequis
1. **XAMPP/WAMP/MAMP installé** et démarré
2. **Base de données créée** avec le fichier `database/schema.sql`
3. **Serveur Apache et MySQL** en fonctionnement

### Étape 2 : Configuration de l'API
1. **Copiez le dossier `api`** dans votre dossier web :
   - XAMPP : `C:\xampp\htdocs\api\`
   - WAMP : `C:\wamp64\www\api\`
   - MAMP : `/Applications/MAMP/htdocs/api/`

2. **Modifiez `api/config/database.php`** :
   ```php
   private $host = 'localhost';
   private $db_name = 'cmc_agriculture';
   private $username = 'root';
   private $password = ''; // Votre mot de passe MySQL
   ```

### Étape 3 : Test de connexion
1. **Accédez à la page de test** : `http://localhost:8081/test-database`
2. **Vérifiez les résultats** :
   - ✅ **Vert** = Connexion réussie
   - ❌ **Rouge** = Problème de connexion

### Étape 4 : Tests manuels
1. **Test direct de l'API** : `http://localhost/api/test-connection.php`
2. **Test des actualités** : `http://localhost/api/actualites.php`

## 🔧 Solutions aux problèmes courants

### Problème 1 : "Impossible de se connecter à l'API"
**Cause** : Serveur web non démarré
**Solution** :
- Démarrez Apache dans XAMPP/WAMP
- Vérifiez que `http://localhost` fonctionne

### Problème 2 : "Base de données introuvable"
**Cause** : Base de données non créée
**Solution** :
- Ouvrez phpMyAdmin : `http://localhost/phpmyadmin`
- Créez la base `cmc_agriculture`
- Importez `database/schema.sql`

### Problème 3 : "Erreur de connexion MySQL"
**Cause** : Paramètres incorrects
**Solution** :
- Vérifiez le mot de passe MySQL
- Modifiez `api/config/database.php`

### Problème 4 : "CORS Error"
**Cause** : Problème de sécurité navigateur
**Solution** :
- Les headers CORS sont déjà configurés dans l'API
- Vérifiez que l'URL de l'API est correcte

## 📊 Indicateurs de réussite

### ✅ Connexion réussie
- Badge vert "Connecté"
- Nombre d'actualités, formations, événements affiché
- Tests API fonctionnels

### ❌ Connexion échouée
- Badge rouge "Erreur"
- Message d'erreur détaillé
- Solutions proposées

## 🚀 Prochaines étapes

Une fois la connexion établie :
1. **Remplacer les données statiques** par des appels API
2. **Créer les endpoints** pour l'admin (CRUD)
3. **Implémenter l'authentification**
4. **Tester toutes les fonctionnalités**

## 📝 URLs de test importantes

- **Page de test** : `http://localhost:8081/test-database`
- **API test** : `http://localhost/api/test-connection.php`
- **API actualités** : `http://localhost/api/actualites.php`
- **phpMyAdmin** : `http://localhost/phpmyadmin`

Suivez ce guide pour vérifier que votre site est bien connecté à la base de données ! 🎉
