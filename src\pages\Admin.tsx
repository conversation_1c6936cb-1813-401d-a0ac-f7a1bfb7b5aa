import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Plus, PenSquare, Trash2, Upload, Eye, Download, RefreshCw } from "lucide-react";
import api from "@/services/api";

const Admin = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginData, setLoginData] = useState({ email: "", password: "" });
  const [activeTab, setActiveTab] = useState("actualites");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState("");
  const [selectedFiliere, setSelectedFiliere] = useState("");
  const [selectedGroupe, setSelectedGroupe] = useState("");
  const [modules, setModules] = useState([""]);
  const [newModule, setNewModule] = useState("");
  const [loading, setLoading] = useState(false);
  const [apiAvailable, setApiAvailable] = useState(false);

  // États pour les données
  const [actualites, setActualites] = useState([]);
  const [evenements, setEvenements] = useState([]);
  const [formations, setFormations] = useState([]);
  const [emploisDuTemps, setEmploisDuTemps] = useState([]);

  const { toast } = useToast();

  // Charger les données depuis l'API
  const loadData = async () => {
    setLoading(true);
    try {
      // Vérifier si l'API est disponible
      const isAvailable = await api.utils.isApiAvailable();
      setApiAvailable(isAvailable);
      
      if (isAvailable) {
        // Charger les actualités
        const actualitesResponse = await api.actualites.getAll();
        if (actualitesResponse.status === 'success') {
          setActualites(actualitesResponse.data.map((item: any) => ({
            ...item,
            category: item.category_code || item.category,
            excerpt: item.resume,
            content: item.contenu,
            author: item.auteur,
            readTime: item.temps_lecture,
            image: item.image_url,
            date: item.date_publication
          })));
        }

        // Charger les formations
        const formationsResponse = await api.formations.getAll();
        if (formationsResponse.status === 'success') {
          setFormations(formationsResponse.data.map((item: any) => ({
            ...item,
            title: item.titre,
            category: item.category_code || item.category,
            level: item.niveau,
            duration: item.duree,
            description: item.description,
            prerequisites: item.prerequis,
            outcomes: item.debouches,
            startDate: item.date_debut,
            places: item.places_max,
            fees: item.frais,
            status: item.statut,
            badge: item.badge,
            image: item.image_url,
            modules: item.modules || []
          })));
        }

        // Charger les événements
        const evenementsResponse = await api.evenements.getAll();
        if (evenementsResponse.status === 'success') {
          setEvenements(evenementsResponse.data.map((item: any) => ({
            ...item,
            title: item.titre,
            description: item.description,
            date: item.date_evenement,
            time: item.time || '',
            location: item.lieu,
            category: item.category_code || item.category,
            status: item.statut,
            image: item.image_url
          })));
        }

        toast({
          title: "Données chargées",
          description: "Les données ont été chargées depuis la base de données."
        });
      } else {
        toast({
          title: "API non disponible",
          description: "Utilisation des données locales. Vérifiez la connexion à l'API.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      toast({
        title: "Erreur de chargement",
        description: "Impossible de charger les données depuis l'API.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Charger les données au montage du composant
  useEffect(() => {
    loadData();
  }, []);

  const handleLogin = (e) => {
    e.preventDefault();
    if (loginData.email === "<EMAIL>" && loginData.password === "password") {
      setIsLoggedIn(true);
      toast({
        title: "Connexion réussie",
        description: "Bienvenue dans l'interface d'administration."
      });
    } else {
      toast({
        title: "Erreur de connexion",
        description: "Email ou mot de passe incorrect.",
        variant: "destructive"
      });
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setLoginData({ email: "", password: "" });
  };

  const handleAdd = () => {
    setEditingItem(null);
    setIsAddDialogOpen(true);
    setSelectedFile(null);
    setPreviewUrl("");
    setSelectedFiliere("");
    setSelectedGroupe("");
    setModules([""]);
    setNewModule("");
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setIsAddDialogOpen(true);
    setSelectedFile(null);
    setPreviewUrl(item.image || item.fileUrl || "");
    
    if (activeTab === "emplois") {
      setSelectedFiliere(item.filiere);
      setSelectedGroupe(item.groupe);
    }
    
    if (activeTab === "formations" && item.modules) {
      setModules(item.modules.length > 0 ? item.modules : [""]);
    }
  };

  const handleDelete = async (id) => {
    setLoading(true);
    
    try {
      if (apiAvailable) {
        // Supprimer via API
        if (activeTab === "actualites") {
          await api.actualites.delete(id);
        } else if (activeTab === "evenements") {
          await api.evenements.delete(id);
        } else if (activeTab === "formations") {
          await api.formations.delete(id);
        }
        
        // Recharger les données
        await loadData();
      } else {
        // Fallback vers localStorage
        if (activeTab === "emplois") {
          setEmploisDuTemps(prev => prev.filter(item => item.id !== id));
        } else if (activeTab === "actualites") {
          setActualites(prev => prev.filter(item => item.id !== id));
        } else if (activeTab === "evenements") {
          setEvenements(prev => prev.filter(item => item.id !== id));
        } else if (activeTab === "formations") {
          setFormations(prev => prev.filter(item => item.id !== id));
        }
      }
      
      toast({
        title: "Élément supprimé",
        description: apiAvailable 
          ? "L'élément a été supprimé de la base de données."
          : "L'élément a été supprimé localement."
      });
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast({
        title: "Erreur de suppression",
        description: api.utils.formatError(error),
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    const formData = new FormData(e.target);
    
    try {
      if (activeTab === "actualites") {
        const actualiteData = {
          title: formData.get('title'),
          excerpt: formData.get('excerpt'),
          content: formData.get('content'),
          category: formData.get('category'),
          status: formData.get('status'),
          author: formData.get('author'),
          readTime: formData.get('readTime'),
          image: formData.get('image'),
          date: editingItem?.date || new Date().toISOString().split('T')[0]
        };

        if (apiAvailable) {
          if (editingItem) {
            await api.actualites.update(editingItem.id, actualiteData);
          } else {
            await api.actualites.create(actualiteData);
          }
          await loadData();
        } else {
          const newActualite = {
            id: editingItem ? editingItem.id : Date.now(),
            ...actualiteData
          };

          if (editingItem) {
            setActualites(prev => prev.map(item => 
              item.id === editingItem.id ? { ...newActualite, date: editingItem.date } : item
            ));
          } else {
            setActualites(prev => [...prev, newActualite]);
          }
        }
      } else if (activeTab === "evenements") {
        const evenementData = {
          title: formData.get('title'),
          description: formData.get('description'),
          date: formData.get('date'),
          time: formData.get('time'),
          location: formData.get('location'),
          category: formData.get('category'),
          status: formData.get('status'),
          image: formData.get('image')
        };

        if (apiAvailable) {
          if (editingItem) {
            await api.evenements.update(editingItem.id, evenementData);
          } else {
            await api.evenements.create(evenementData);
          }
          await loadData();
        } else {
          const newEvenement = {
            id: editingItem ? editingItem.id : Date.now(),
            ...evenementData
          };

          if (editingItem) {
            setEvenements(prev => prev.map(item => 
              item.id === editingItem.id ? newEvenement : item
            ));
          } else {
            setEvenements(prev => [...prev, newEvenement]);
          }
        }
      } else if (activeTab === "formations") {
        const formationData = {
          title: formData.get('title'),
          description: formData.get('description'),
          category: formData.get('category'),
          level: formData.get('level'),
          duration: formData.get('duration'),
          prerequisites: formData.get('prerequisites'),
          outcomes: formData.get('outcomes'),
          startDate: formData.get('startDate'),
          places: parseInt(formData.get('places')),
          fees: parseInt(formData.get('fees')),
          status: formData.get('status'),
          badge: formData.get('badge'),
          image: formData.get('image'),
          modules: modules.filter(module => module.trim() !== "")
        };

        if (apiAvailable) {
          if (editingItem) {
            await api.formations.update(editingItem.id, formationData);
          } else {
            await api.formations.create(formationData);
          }
          await loadData();
        } else {
          const newFormation = {
            id: editingItem ? editingItem.id : Date.now(),
            ...formationData
          };

          if (editingItem) {
            setFormations(prev => prev.map(item => 
              item.id === editingItem.id ? newFormation : item
            ));
          } else {
            setFormations(prev => [...prev, newFormation]);
          }
        }
      }
      
      setIsAddDialogOpen(false);
      setEditingItem(null);
      setSelectedFile(null);
      setPreviewUrl("");
      setSelectedFiliere("");
      setSelectedGroupe("");
      setModules([""]);
      setNewModule("");
      
      toast({
        title: "Modifications enregistrées",
        description: apiAvailable 
          ? "Les changements ont été sauvegardés dans la base de données."
          : "Les changements ont été sauvegardés localement."
      });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast({
        title: "Erreur de sauvegarde",
        description: api.utils.formatError(error),
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Accès Administrateur</CardTitle>
              <CardDescription>
                Veuillez vous authentifier avec vos identifiants.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={loginData.email}
                    onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Mot de passe</Label>
                  <Input
                    id="password"
                    type="password"
                    value={loginData.password}
                    onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                    required
                  />
                </div>
                <Button type="submit" className="w-full">Se connecter</Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Interface d'administration</h1>
              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${apiAvailable ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-sm text-gray-600">
                    {apiAvailable ? 'Base de données connectée' : 'Mode local (données temporaires)'}
                  </span>
                </div>
                <Button 
                  onClick={loadData} 
                  disabled={loading}
                  size="sm"
                  variant="outline"
                >
                  {loading ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
                  Actualiser
                </Button>
              </div>
            </div>
            <Button onClick={handleLogout} variant="outline">Se déconnecter</Button>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 gap-4 mb-8">
            <TabsTrigger value="actualites">Actualités</TabsTrigger>
            <TabsTrigger value="evenements">Événements</TabsTrigger>
            <TabsTrigger value="formations">Formations</TabsTrigger>
          </TabsList>

          <TabsContent value="actualites">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des actualités</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une actualité
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Catégorie</TableHead>
                      <TableHead>Auteur</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {actualites.map((actualite) => (
                      <TableRow key={actualite.id}>
                        <TableCell className="max-w-xs">
                          <div>
                            <div className="font-medium truncate">{actualite.title}</div>
                            <div className="text-sm text-muted-foreground truncate">{actualite.excerpt}</div>
                          </div>
                        </TableCell>
                        <TableCell>{actualite.date}</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {actualite.category === "infrastructure" && "Infrastructure"}
                            {actualite.category === "evenement" && "Événement"}
                            {actualite.category === "formation" && "Formation"}
                            {actualite.category === "partenariat" && "Partenariat"}
                            {actualite.category === "reussite" && "Réussite"}
                          </Badge>
                        </TableCell>
                        <TableCell>{actualite.author}</TableCell>
                        <TableCell>
                          <Badge variant={actualite.status === "publié" ? "default" : "secondary"}>
                            {actualite.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(actualite)}
                              title="Modifier"
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(actualite.id)}
                              title="Supprimer"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {actualites.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucune actualité trouvée. Cliquez sur "Ajouter une actualité" pour commencer.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Autres onglets seront ajoutés dans la suite... */}
        </Tabs>
      </main>
    </div>
  );
};

export default Admin;
