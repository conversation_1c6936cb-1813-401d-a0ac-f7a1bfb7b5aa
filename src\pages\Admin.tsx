
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Plus, PenSquare, Trash2, Upload, Eye, Download } from "lucide-react";

const Admin = () => {
  const { toast } = useToast();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginData, setLoginData] = useState({
    email: "",
    password: ""
  });
  const [activeTab, setActiveTab] = useState("actualites");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState("");
  const [selectedFiliere, setSelectedFiliere] = useState("");
  const [selectedGroupe, setSelectedGroupe] = useState("");
  const [modules, setModules] = useState([""]);
  const [newModule, setNewModule] = useState("");

  // Données d'exemple - Actualités complètes
  const [actualites, setActualites] = useState([
    {
      id: 1,
      title: "Inauguration du nouveau laboratoire d'analyse des sols",
      date: "2024-03-20",
      category: "infrastructure",
      status: "publié",
      excerpt: "Un nouveau laboratoire d'analyse des sols a été inauguré au sein du Pôle Agriculture, renforçant les capacités de recherche et de formation.",
      content: "Le Pôle Agriculture de la Cité des Métiers et des Compétences a inauguré ce mardi un nouveau laboratoire d'analyse des sols, équipé des technologies les plus récentes pour l'étude et l'analyse des propriétés physiques, chimiques et biologiques des sols.",
      image: "https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80",
      author: "Service communication",
      readTime: "3 min"
    },
    {
      id: 2,
      title: "Journée portes ouvertes: venez découvrir nos formations agricoles",
      date: "2024-04-15",
      category: "evenement",
      status: "publié",
      excerpt: "Le Pôle Agriculture organise une journée portes ouvertes le 15 avril 2025. Une occasion unique de découvrir nos installations et nos programmes de formation.",
      content: "Le Pôle Agriculture de la CMC ouvre ses portes au public le 15 avril prochain. Cette journée sera l'occasion pour les futurs stagiaires, leurs familles et toute personne intéressée par le secteur agricole de découvrir notre campus, nos installations techniques et nos différentes filières de formation.",
      image: "https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80",
      author: "Service communication",
      readTime: "4 min"
    },
    {
      id: 3,
      title: "Partenariat avec l'Université Mohammed VI Polytechnique",
      date: "2024-03-10",
      category: "partenariat",
      status: "brouillon",
      excerpt: "Signature d'un accord de partenariat pour développer des programmes de recherche conjoints en agriculture durable.",
      content: "Un nouveau partenariat stratégique a été signé avec l'Université Mohammed VI Polytechnique pour développer des programmes de recherche conjoints en agriculture durable et former les futurs experts du secteur.",
      image: "https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80",
      author: "Direction",
      readTime: "2 min"
    }
  ]);

  // Données des formations
  const [formations, setFormations] = useState([
    {
      id: 1,
      title: "Technicien en Production Végétale",
      category: "culture",
      badge: "Populaire",
      level: "Technicien",
      duration: "24 mois",
      description: "Formation complète en production végétale couvrant les cultures maraîchères, céréalières et arboricoles avec les techniques modernes de production.",
      modules: [
        "Agronomie générale et pédologie",
        "Production des cultures maraîchères",
        "Production des cultures céréalières",
        "Arboriculture fruitière",
        "Protection phytosanitaire",
        "Irrigation et fertilisation",
        "Mécanisation agricole",
        "Post-récolte et conditionnement"
      ],
      prerequisites: "Niveau baccalauréat scientifique ou technique",
      outcomes: "Technicien capable de gérer une exploitation agricole spécialisée en production végétale",
      startDate: "Septembre 2025",
      places: 25,
      fees: 1500,
      status: "publié",
      image: "https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 2,
      title: "Technicien Spécialisé en Élevage",
      category: "elevage",
      badge: "Nouvelle session",
      level: "Technicien Spécialisé",
      duration: "30 mois",
      description: "Formation avancée en élevage couvrant tous les aspects de la production animale : bovins, ovins, caprins, aviculture et apiculture.",
      modules: [
        "Zootechnie générale",
        "Nutrition et alimentation animale",
        "Reproduction et amélioration génétique",
        "Pathologie et prophylaxie",
        "Gestion technico-économique d'élevage",
        "Transformation des produits animaux",
        "Commercialisation des produits d'élevage",
        "Législation et réglementation"
      ],
      prerequisites: "Niveau baccalauréat, de préférence scientifique",
      outcomes: "Technicien spécialisé en élevage capable de gérer des exploitations d'élevage modernes",
      startDate: "Octobre 2025",
      places: 20,
      fees: 1500,
      status: "publié",
      image: "https://images.unsplash.com/photo-1500595046743-cd271d694d30?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 3,
      title: "Gestionnaire d'Exploitation Agricole",
      category: "gestion",
      badge: "",
      level: "Technicien Spécialisé",
      duration: "24 mois",
      description: "Formation en gestion d'exploitation agricole combinant aspects techniques, économiques et managériaux pour une gestion optimale des ressources.",
      modules: [
        "Économie et gestion d'entreprise agricole",
        "Comptabilité et analyse financière",
        "Marketing et commercialisation",
        "Gestion des ressources humaines",
        "Techniques de production agricole",
        "Gestion de la qualité et traçabilité",
        "Informatique appliquée à l'agriculture",
        "Législation agricole et foncière"
      ],
      prerequisites: "Niveau baccalauréat, expérience en agriculture appréciée",
      outcomes: "Gestionnaire capable de diriger une exploitation agricole moderne et rentable",
      startDate: "Septembre 2025",
      places: 15,
      fees: 1500,
      status: "brouillon",
      image: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    }
  ]);

  // Données des événements
  const [evenements, setEvenements] = useState([
    {
      id: 1,
      title: "Journée portes ouvertes",
      date: "2025-04-15",
      time: "9h-17h",
      location: "Campus CMC Agriculture",
      description: "Découvrez nos formations et nos installations lors de notre journée portes ouvertes annuelle.",
      category: "portes-ouvertes",
      status: "publié",
      image: "https://images.unsplash.com/photo-1523348837708-15d4a09cfac2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 2,
      title: "Conférence internationale sur l'agriculture du futur",
      date: "2025-05-05",
      time: "8h30-18h",
      location: "Auditorium principal CMC",
      description: "Une journée d'échanges et de conférences sur l'avenir de l'agriculture au Maroc et en Afrique.",
      category: "conference",
      status: "publié",
      image: "https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    },
    {
      id: 3,
      title: "Atelier pratique: Techniques d'agriculture urbaine",
      date: "2025-05-20",
      time: "14h-17h",
      location: "Serre pédagogique",
      description: "Apprenez les bases de l'agriculture urbaine et les techniques adaptées aux petits espaces.",
      category: "atelier",
      status: "brouillon",
      image: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
    }
  ]);

  // Données des emplois du temps
  const [emploisDuTemps, setEmploisDuTemps] = useState([
    {
      id: 1,
      filiere: "culture",
      filiereLabel: "Culture",
      groupe: "c1",
      groupeLabel: "Culture - Groupe 1",
      fileName: "culture-c1.png",
      uploadDate: "2024-03-15",
      fileUrl: "/images/emplois-du-temps/culture-c1.png"
    },
    {
      id: 2,
      filiere: "elevage",
      filiereLabel: "Élevage",
      groupe: "e1",
      groupeLabel: "Élevage - Groupe 1",
      fileName: "elevage-e1.png",
      uploadDate: "2024-03-10",
      fileUrl: "/images/emplois-du-temps/elevage-e1.png"
    }
  ]);

  // Configuration des filières et groupes
  const filieres = [
    { value: "culture", label: "Culture" },
    { value: "elevage", label: "Élevage" },
    { value: "gestion", label: "Gestion d'exploitation" },
    { value: "durable", label: "Agriculture durable" }
  ];

  const groupes = {
    culture: [
      { value: "c1", label: "Culture - Groupe 1" },
      { value: "c2", label: "Culture - Groupe 2" }
    ],
    elevage: [
      { value: "e1", label: "Élevage - Groupe 1" },
      { value: "e2", label: "Élevage - Groupe 2" }
    ],
    gestion: [
      { value: "g1", label: "Gestion - Groupe 1" }
    ],
    durable: [
      { value: "d1", label: "Agriculture durable - Groupe 1" },
      { value: "d2", label: "Agriculture durable - Groupe 2" }
    ]
  };

  const handleLogin = (e) => {
    e.preventDefault();
    // Vérification des identifiants (à implémenter avec le backend)
    setIsLoggedIn(true);
    toast({
      title: "Connexion réussie",
      description: "Bienvenue dans l'interface d'administration."
    });
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setLoginData({ email: "", password: "" });
  };

  const handleAdd = () => {
    setEditingItem(null);
    setSelectedFile(null);
    setPreviewUrl("");
    setSelectedFiliere("");
    setSelectedGroupe("");
    setModules([""]);
    setNewModule("");
    setIsAddDialogOpen(true);
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    if (activeTab === "emplois") {
      setSelectedFiliere(item.filiere);
      setSelectedGroupe(item.groupe);
      setPreviewUrl(item.fileUrl);
    } else if (activeTab === "formations") {
      setModules(item.modules || [""]);
    }
    setIsAddDialogOpen(true);
  };

  const handleDelete = (id) => {
    if (activeTab === "emplois") {
      setEmploisDuTemps(prev => prev.filter(item => item.id !== id));
    } else if (activeTab === "actualites") {
      setActualites(prev => prev.filter(item => item.id !== id));
    } else if (activeTab === "evenements") {
      setEvenements(prev => prev.filter(item => item.id !== id));
    } else if (activeTab === "formations") {
      setFormations(prev => prev.filter(item => item.id !== id));
    }

    toast({
      title: "Élément supprimé",
      description: "L'élément a été supprimé avec succès."
    });
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleFiliereChange = (value) => {
    setSelectedFiliere(value);
    setSelectedGroupe("");
  };

  const getSelectedGroupes = () => {
    return selectedFiliere ? groupes[selectedFiliere] || [] : [];
  };

  const addModule = () => {
    if (newModule.trim()) {
      setModules(prev => [...prev, newModule.trim()]);
      setNewModule("");
    }
  };

  const removeModule = (index) => {
    setModules(prev => prev.filter((_, i) => i !== index));
  };

  const updateModule = (index, value) => {
    setModules(prev => prev.map((module, i) => i === index ? value : module));
  };

  const handleSave = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);

    if (activeTab === "actualites") {
      const newActualite = {
        id: editingItem ? editingItem.id : Date.now(),
        title: formData.get('title'),
        excerpt: formData.get('excerpt'),
        content: formData.get('content'),
        category: formData.get('category'),
        status: formData.get('status'),
        author: formData.get('author'),
        readTime: formData.get('readTime'),
        image: formData.get('image'),
        date: new Date().toISOString().split('T')[0]
      };

      if (editingItem) {
        setActualites(prev => prev.map(item =>
          item.id === editingItem.id ? { ...newActualite, date: editingItem.date } : item
        ));
      } else {
        setActualites(prev => [...prev, newActualite]);
      }
    } else if (activeTab === "evenements") {
      const newEvenement = {
        id: editingItem ? editingItem.id : Date.now(),
        title: formData.get('title'),
        description: formData.get('description'),
        date: formData.get('date'),
        time: formData.get('time'),
        location: formData.get('location'),
        category: formData.get('category'),
        status: formData.get('status'),
        image: formData.get('image')
      };

      if (editingItem) {
        setEvenements(prev => prev.map(item =>
          item.id === editingItem.id ? newEvenement : item
        ));
      } else {
        setEvenements(prev => [...prev, newEvenement]);
      }
    } else if (activeTab === "formations") {
      const newFormation = {
        id: editingItem ? editingItem.id : Date.now(),
        title: formData.get('title'),
        description: formData.get('description'),
        category: formData.get('category'),
        level: formData.get('level'),
        duration: formData.get('duration'),
        prerequisites: formData.get('prerequisites'),
        outcomes: formData.get('outcomes'),
        startDate: formData.get('startDate'),
        places: parseInt(formData.get('places')),
        fees: parseInt(formData.get('fees')),
        status: formData.get('status'),
        badge: formData.get('badge'),
        image: formData.get('image'),
        modules: modules.filter(module => module.trim() !== "")
      };

      if (editingItem) {
        setFormations(prev => prev.map(item =>
          item.id === editingItem.id ? newFormation : item
        ));
      } else {
        setFormations(prev => [...prev, newFormation]);
      }
    } else if (activeTab === "emplois") {
      const filiereData = filieres.find(f => f.value === selectedFiliere);
      const groupeData = getSelectedGroupes().find(g => g.value === selectedGroupe);

      if (!selectedFiliere || !selectedGroupe) {
        toast({
          title: "Erreur",
          description: "Veuillez sélectionner une filière et un groupe.",
          variant: "destructive"
        });
        return;
      }

      if (!selectedFile && !editingItem) {
        toast({
          title: "Erreur",
          description: "Veuillez sélectionner un fichier.",
          variant: "destructive"
        });
        return;
      }

      const newEmploi = {
        id: editingItem ? editingItem.id : Date.now(),
        filiere: selectedFiliere,
        filiereLabel: filiereData.label,
        groupe: selectedGroupe,
        groupeLabel: groupeData.label,
        fileName: selectedFile ? selectedFile.name : editingItem?.fileName,
        uploadDate: new Date().toISOString().split('T')[0],
        fileUrl: previewUrl || editingItem?.fileUrl
      };

      if (editingItem) {
        setEmploisDuTemps(prev => prev.map(item =>
          item.id === editingItem.id ? newEmploi : item
        ));
      } else {
        setEmploisDuTemps(prev => [...prev, newEmploi]);
      }
    }

    setIsAddDialogOpen(false);
    setEditingItem(null);
    setSelectedFile(null);
    setPreviewUrl("");
    setSelectedFiliere("");
    setSelectedGroupe("");
    setModules([""]);
    setNewModule("");

    toast({
      title: "Modifications enregistrées",
      description: "Les changements ont été sauvegardés avec succès."
    });
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Accès Administrateur</CardTitle>
              <CardDescription>
                Veuillez vous authentifier avec vos identifiants.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={loginData.email}
                    onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Mot de passe</Label>
                  <Input
                    id="password"
                    type="password"
                    value={loginData.password}
                    onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                    required
                  />
                </div>
                <Button type="submit" className="w-full">Se connecter</Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">Interface d'administration</h1>
            <Button onClick={handleLogout} variant="outline">Se déconnecter</Button>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 gap-4 mb-8">
            <TabsTrigger value="actualites">Actualités</TabsTrigger>
            <TabsTrigger value="evenements">Événements</TabsTrigger>
            <TabsTrigger value="formations">Formations</TabsTrigger>
            <TabsTrigger value="emplois">Emplois du temps</TabsTrigger>
          </TabsList>

          <TabsContent value="actualites">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des actualités</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une actualité
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Catégorie</TableHead>
                      <TableHead>Auteur</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {actualites.map((actualite) => (
                      <TableRow key={actualite.id}>
                        <TableCell className="max-w-xs">
                          <div>
                            <div className="font-medium truncate">{actualite.title}</div>
                            <div className="text-sm text-muted-foreground truncate">{actualite.excerpt}</div>
                          </div>
                        </TableCell>
                        <TableCell>{actualite.date}</TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {actualite.category === "infrastructure" && "Infrastructure"}
                            {actualite.category === "evenement" && "Événement"}
                            {actualite.category === "formation" && "Formation"}
                            {actualite.category === "partenariat" && "Partenariat"}
                            {actualite.category === "reussite" && "Réussite"}
                          </Badge>
                        </TableCell>
                        <TableCell>{actualite.author}</TableCell>
                        <TableCell>
                          <Badge variant={actualite.status === "publié" ? "default" : "secondary"}>
                            {actualite.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.open(`/actualites#${actualite.id}`, '_blank')}
                              title="Voir l'actualité"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(actualite)}
                              title="Modifier"
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(actualite.id)}
                              title="Supprimer"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {actualites.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucune actualité trouvée. Cliquez sur "Ajouter une actualité" pour commencer.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="evenements">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des événements</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter un événement
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Heure</TableHead>
                      <TableHead>Lieu</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {evenements.map((evenement) => (
                      <TableRow key={evenement.id}>
                        <TableCell className="max-w-xs">
                          <div>
                            <div className="font-medium truncate">{evenement.title}</div>
                            <div className="text-sm text-muted-foreground truncate">{evenement.description}</div>
                          </div>
                        </TableCell>
                        <TableCell>{evenement.date}</TableCell>
                        <TableCell>{evenement.time}</TableCell>
                        <TableCell>{evenement.location}</TableCell>
                        <TableCell>
                          <Badge variant={evenement.status === "publié" ? "default" : "secondary"}>
                            {evenement.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.open(`/actualites#evenements`, '_blank')}
                              title="Voir l'événement"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(evenement)}
                              title="Modifier"
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(evenement.id)}
                              title="Supprimer"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {evenements.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucun événement trouvé. Cliquez sur "Ajouter un événement" pour commencer.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="formations">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des formations</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une formation
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Formation</TableHead>
                      <TableHead>Catégorie</TableHead>
                      <TableHead>Niveau</TableHead>
                      <TableHead>Durée</TableHead>
                      <TableHead>Places</TableHead>
                      <TableHead>Début</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {formations.map((formation) => (
                      <TableRow key={formation.id}>
                        <TableCell className="max-w-xs">
                          <div>
                            <div className="font-medium truncate">{formation.title}</div>
                            <div className="text-sm text-muted-foreground truncate">{formation.description}</div>
                            {formation.badge && (
                              <Badge className="mt-1 bg-agro-600 text-xs">{formation.badge}</Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {formation.category === "culture" && "Culture"}
                            {formation.category === "elevage" && "Élevage"}
                            {formation.category === "gestion" && "Gestion"}
                            {formation.category === "durable" && "Agriculture durable"}
                          </Badge>
                        </TableCell>
                        <TableCell>{formation.level}</TableCell>
                        <TableCell>{formation.duration}</TableCell>
                        <TableCell>{formation.places} places</TableCell>
                        <TableCell>{formation.startDate}</TableCell>
                        <TableCell>
                          <Badge variant={formation.status === "publié" ? "default" : "secondary"}>
                            {formation.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.open(`/formations#${formation.id}`, '_blank')}
                              title="Voir la formation"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(formation)}
                              title="Modifier"
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(formation.id)}
                              title="Supprimer"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {formations.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucune formation trouvée. Cliquez sur "Ajouter une formation" pour commencer.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="emplois">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des emplois du temps</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter un emploi du temps
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Filière</TableHead>
                      <TableHead>Groupe</TableHead>
                      <TableHead>Nom du fichier</TableHead>
                      <TableHead>Date d'upload</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {emploisDuTemps.map((emploi) => (
                      <TableRow key={emploi.id}>
                        <TableCell>
                          <Badge variant="outline">{emploi.filiereLabel}</Badge>
                        </TableCell>
                        <TableCell>{emploi.groupeLabel}</TableCell>
                        <TableCell>{emploi.fileName}</TableCell>
                        <TableCell>{emploi.uploadDate}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.open(emploi.fileUrl, '_blank')}
                              title="Voir le fichier"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(emploi)}
                              title="Modifier"
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(emploi.id)}
                              title="Supprimer"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {emploisDuTemps.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucun emploi du temps trouvé. Cliquez sur "Ajouter un emploi du temps" pour commencer.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingItem ? "Modifier" : "Ajouter"} un élément
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSave} className="space-y-4">
              <div className="space-y-4">
                {activeTab === "actualites" && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="title">Titre</Label>
                      <Input
                        id="title"
                        name="title"
                        defaultValue={editingItem?.title}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="excerpt">Résumé</Label>
                      <Textarea
                        id="excerpt"
                        name="excerpt"
                        defaultValue={editingItem?.excerpt}
                        placeholder="Résumé court de l'actualité..."
                        rows={2}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="content">Contenu complet</Label>
                      <Textarea
                        id="content"
                        name="content"
                        defaultValue={editingItem?.content}
                        placeholder="Contenu détaillé de l'actualité..."
                        rows={6}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="category">Catégorie</Label>
                        <Select name="category" defaultValue={editingItem?.category}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner une catégorie" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="evenement">Événement</SelectItem>
                            <SelectItem value="formation">Formation</SelectItem>
                            <SelectItem value="infrastructure">Infrastructure</SelectItem>
                            <SelectItem value="partenariat">Partenariat</SelectItem>
                            <SelectItem value="reussite">Réussite</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="status">Statut</Label>
                        <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un statut" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="brouillon">Brouillon</SelectItem>
                            <SelectItem value="publié">Publié</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="author">Auteur</Label>
                        <Input
                          id="author"
                          name="author"
                          defaultValue={editingItem?.author || "Service communication"}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="readTime">Temps de lecture</Label>
                        <Input
                          id="readTime"
                          name="readTime"
                          defaultValue={editingItem?.readTime || "3 min"}
                          placeholder="ex: 3 min"
                          required
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="image">URL de l'image</Label>
                      <Input
                        id="image"
                        name="image"
                        type="url"
                        defaultValue={editingItem?.image}
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>
                  </>
                )}

                {activeTab === "evenements" && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="title">Titre de l'événement</Label>
                      <Input
                        id="title"
                        name="title"
                        defaultValue={editingItem?.title}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        defaultValue={editingItem?.description}
                        placeholder="Description de l'événement..."
                        rows={3}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="date">Date</Label>
                        <Input
                          id="date"
                          name="date"
                          type="date"
                          defaultValue={editingItem?.date}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="time">Heure</Label>
                        <Input
                          id="time"
                          name="time"
                          defaultValue={editingItem?.time}
                          placeholder="ex: 9h-17h"
                          required
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="location">Lieu</Label>
                      <Input
                        id="location"
                        name="location"
                        defaultValue={editingItem?.location}
                        placeholder="ex: Campus CMC Agriculture"
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="category">Type d'événement</Label>
                        <Select name="category" defaultValue={editingItem?.category}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="portes-ouvertes">Portes ouvertes</SelectItem>
                            <SelectItem value="conference">Conférence</SelectItem>
                            <SelectItem value="atelier">Atelier</SelectItem>
                            <SelectItem value="forum">Forum</SelectItem>
                            <SelectItem value="formation">Formation</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="status">Statut</Label>
                        <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un statut" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="brouillon">Brouillon</SelectItem>
                            <SelectItem value="publié">Publié</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="image">URL de l'image</Label>
                      <Input
                        id="image"
                        name="image"
                        type="url"
                        defaultValue={editingItem?.image}
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>
                  </>
                )}

                {activeTab === "formations" && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="title">Titre de la formation</Label>
                      <Input
                        id="title"
                        name="title"
                        defaultValue={editingItem?.title}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        defaultValue={editingItem?.description}
                        placeholder="Description complète de la formation..."
                        rows={4}
                        required
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="category">Catégorie</Label>
                        <Select name="category" defaultValue={editingItem?.category}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner une catégorie" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="culture">Culture</SelectItem>
                            <SelectItem value="elevage">Élevage</SelectItem>
                            <SelectItem value="gestion">Gestion d'exploitation</SelectItem>
                            <SelectItem value="durable">Agriculture durable</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="level">Niveau</Label>
                        <Select name="level" defaultValue={editingItem?.level}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un niveau" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Technicien">Technicien</SelectItem>
                            <SelectItem value="Technicien Spécialisé">Technicien Spécialisé</SelectItem>
                            <SelectItem value="Qualification">Qualification</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="duration">Durée</Label>
                        <Input
                          id="duration"
                          name="duration"
                          defaultValue={editingItem?.duration}
                          placeholder="ex: 24 mois"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="places">Nombre de places</Label>
                        <Input
                          id="places"
                          name="places"
                          type="number"
                          defaultValue={editingItem?.places}
                          min="1"
                          max="50"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="fees">Frais (MAD)</Label>
                        <Input
                          id="fees"
                          name="fees"
                          type="number"
                          defaultValue={editingItem?.fees}
                          min="0"
                          required
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="startDate">Date de début</Label>
                        <Input
                          id="startDate"
                          name="startDate"
                          defaultValue={editingItem?.startDate}
                          placeholder="ex: Septembre 2025"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="status">Statut</Label>
                        <Select name="status" defaultValue={editingItem?.status || "brouillon"}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un statut" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="brouillon">Brouillon</SelectItem>
                            <SelectItem value="publié">Publié</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="prerequisites">Prérequis</Label>
                      <Textarea
                        id="prerequisites"
                        name="prerequisites"
                        defaultValue={editingItem?.prerequisites}
                        placeholder="Conditions d'admission et prérequis..."
                        rows={2}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="outcomes">Débouchés</Label>
                      <Textarea
                        id="outcomes"
                        name="outcomes"
                        defaultValue={editingItem?.outcomes}
                        placeholder="Opportunités professionnelles après la formation..."
                        rows={2}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="badge">Badge (optionnel)</Label>
                      <Input
                        id="badge"
                        name="badge"
                        defaultValue={editingItem?.badge}
                        placeholder="ex: Populaire, Nouvelle session"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="image">URL de l'image</Label>
                      <Input
                        id="image"
                        name="image"
                        type="url"
                        defaultValue={editingItem?.image}
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>

                    <div className="space-y-4">
                      <Label>Modules de formation</Label>
                      <div className="space-y-2">
                        {modules.map((module, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Input
                              value={module}
                              onChange={(e) => updateModule(index, e.target.value)}
                              placeholder="Nom du module..."
                              className="flex-1"
                            />
                            <Button
                              type="button"
                              size="sm"
                              variant="destructive"
                              onClick={() => removeModule(index)}
                              disabled={modules.length === 1}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                        <div className="flex items-center space-x-2">
                          <Input
                            value={newModule}
                            onChange={(e) => setNewModule(e.target.value)}
                            placeholder="Ajouter un nouveau module..."
                            className="flex-1"
                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addModule())}
                          />
                          <Button
                            type="button"
                            size="sm"
                            onClick={addModule}
                            disabled={!newModule.trim()}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Ajoutez les modules qui composent cette formation. Vous pouvez en ajouter autant que nécessaire.
                      </p>
                    </div>
                  </>
                )}

                {activeTab === "emplois" && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="filiere">Filière</Label>
                      <Select value={selectedFiliere} onValueChange={handleFiliereChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner une filière" />
                        </SelectTrigger>
                        <SelectContent>
                          {filieres.map((filiere) => (
                            <SelectItem key={filiere.value} value={filiere.value}>
                              {filiere.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {selectedFiliere && (
                      <div className="space-y-2">
                        <Label htmlFor="groupe">Groupe</Label>
                        <Select value={selectedGroupe} onValueChange={setSelectedGroupe}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un groupe" />
                          </SelectTrigger>
                          <SelectContent>
                            {getSelectedGroupes().map((groupe) => (
                              <SelectItem key={groupe.value} value={groupe.value}>
                                {groupe.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="file">Fichier emploi du temps</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          id="file"
                          type="file"
                          accept="image/*,.pdf"
                          onChange={handleFileChange}
                          className="flex-1"
                        />
                        <Upload className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Formats acceptés : PNG, JPG, JPEG, PDF (max 10MB)
                      </p>
                    </div>

                    {previewUrl && (
                      <div className="space-y-2">
                        <Label>Aperçu</Label>
                        <div className="border rounded-lg p-4 bg-muted/50">
                          {selectedFile?.type?.startsWith('image/') || previewUrl.includes('.png') || previewUrl.includes('.jpg') ? (
                            <img
                              src={previewUrl}
                              alt="Aperçu emploi du temps"
                              className="max-w-full h-auto max-h-64 mx-auto rounded"
                            />
                          ) : (
                            <div className="text-center py-8">
                              <Download className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                              <p className="text-sm text-muted-foreground">
                                Fichier PDF - Cliquez pour télécharger
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
              <DialogFooter>
                <Button type="submit">
                  {editingItem ? "Enregistrer les modifications" : "Ajouter"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </main>
    </div>
  );
};

export default Admin;
