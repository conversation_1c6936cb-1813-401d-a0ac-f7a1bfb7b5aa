
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Plus, PenSquare, Trash2, Upload, Eye, Download } from "lucide-react";

const Admin = () => {
  const { toast } = useToast();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginData, setLoginData] = useState({
    email: "",
    password: ""
  });
  const [activeTab, setActiveTab] = useState("actualites");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState("");
  const [selectedFiliere, setSelectedFiliere] = useState("");
  const [selectedGroupe, setSelectedGroupe] = useState("");

  // Données d'exemple
  const actualites = [
    { id: 1, title: "Nouvelle formation", date: "2024-03-20", category: "formation", status: "publié" },
    { id: 2, title: "Événement portes ouvertes", date: "2024-04-15", category: "evenement", status: "brouillon" }
  ];

  // Données des emplois du temps
  const [emploisDuTemps, setEmploisDuTemps] = useState([
    {
      id: 1,
      filiere: "culture",
      filiereLabel: "Culture",
      groupe: "c1",
      groupeLabel: "Culture - Groupe 1",
      fileName: "culture-c1.png",
      uploadDate: "2024-03-15",
      fileUrl: "/images/emplois-du-temps/culture-c1.png"
    },
    {
      id: 2,
      filiere: "elevage",
      filiereLabel: "Élevage",
      groupe: "e1",
      groupeLabel: "Élevage - Groupe 1",
      fileName: "elevage-e1.png",
      uploadDate: "2024-03-10",
      fileUrl: "/images/emplois-du-temps/elevage-e1.png"
    }
  ]);

  // Configuration des filières et groupes
  const filieres = [
    { value: "culture", label: "Culture" },
    { value: "elevage", label: "Élevage" },
    { value: "gestion", label: "Gestion d'exploitation" },
    { value: "durable", label: "Agriculture durable" }
  ];

  const groupes = {
    culture: [
      { value: "c1", label: "Culture - Groupe 1" },
      { value: "c2", label: "Culture - Groupe 2" }
    ],
    elevage: [
      { value: "e1", label: "Élevage - Groupe 1" },
      { value: "e2", label: "Élevage - Groupe 2" }
    ],
    gestion: [
      { value: "g1", label: "Gestion - Groupe 1" }
    ],
    durable: [
      { value: "d1", label: "Agriculture durable - Groupe 1" },
      { value: "d2", label: "Agriculture durable - Groupe 2" }
    ]
  };

  const handleLogin = (e) => {
    e.preventDefault();
    // Vérification des identifiants (à implémenter avec le backend)
    setIsLoggedIn(true);
    toast({
      title: "Connexion réussie",
      description: "Bienvenue dans l'interface d'administration."
    });
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setLoginData({ email: "", password: "" });
  };

  const handleAdd = () => {
    setEditingItem(null);
    setSelectedFile(null);
    setPreviewUrl("");
    setSelectedFiliere("");
    setSelectedGroupe("");
    setIsAddDialogOpen(true);
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    if (activeTab === "emplois") {
      setSelectedFiliere(item.filiere);
      setSelectedGroupe(item.groupe);
      setPreviewUrl(item.fileUrl);
    }
    setIsAddDialogOpen(true);
  };

  const handleDelete = (id) => {
    if (activeTab === "emplois") {
      setEmploisDuTemps(prev => prev.filter(item => item.id !== id));
    }
    toast({
      title: "Élément supprimé",
      description: "L'élément a été supprimé avec succès."
    });
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleFiliereChange = (value) => {
    setSelectedFiliere(value);
    setSelectedGroupe("");
  };

  const getSelectedGroupes = () => {
    return selectedFiliere ? groupes[selectedFiliere] || [] : [];
  };

  const handleSave = (e) => {
    e.preventDefault();

    if (activeTab === "emplois") {
      const filiereData = filieres.find(f => f.value === selectedFiliere);
      const groupeData = getSelectedGroupes().find(g => g.value === selectedGroupe);

      if (!selectedFiliere || !selectedGroupe) {
        toast({
          title: "Erreur",
          description: "Veuillez sélectionner une filière et un groupe.",
          variant: "destructive"
        });
        return;
      }

      if (!selectedFile && !editingItem) {
        toast({
          title: "Erreur",
          description: "Veuillez sélectionner un fichier.",
          variant: "destructive"
        });
        return;
      }

      const newEmploi = {
        id: editingItem ? editingItem.id : Date.now(),
        filiere: selectedFiliere,
        filiereLabel: filiereData.label,
        groupe: selectedGroupe,
        groupeLabel: groupeData.label,
        fileName: selectedFile ? selectedFile.name : editingItem?.fileName,
        uploadDate: new Date().toISOString().split('T')[0],
        fileUrl: previewUrl || editingItem?.fileUrl
      };

      if (editingItem) {
        setEmploisDuTemps(prev => prev.map(item =>
          item.id === editingItem.id ? newEmploi : item
        ));
      } else {
        setEmploisDuTemps(prev => [...prev, newEmploi]);
      }
    }

    setIsAddDialogOpen(false);
    setEditingItem(null);
    setSelectedFile(null);
    setPreviewUrl("");
    setSelectedFiliere("");
    setSelectedGroupe("");

    toast({
      title: "Modifications enregistrées",
      description: "Les changements ont été sauvegardés avec succès."
    });
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Accès Administrateur</CardTitle>
              <CardDescription>
                Veuillez vous authentifier avec vos identifiants.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={loginData.email}
                    onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Mot de passe</Label>
                  <Input
                    id="password"
                    type="password"
                    value={loginData.password}
                    onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                    required
                  />
                </div>
                <Button type="submit" className="w-full">Se connecter</Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">Interface d'administration</h1>
            <Button onClick={handleLogout} variant="outline">Se déconnecter</Button>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 gap-4 mb-8">
            <TabsTrigger value="actualites">Actualités</TabsTrigger>
            <TabsTrigger value="formations">Formations</TabsTrigger>
            <TabsTrigger value="emplois">Emplois du temps</TabsTrigger>
          </TabsList>

          <TabsContent value="actualites">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des actualités</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une actualité
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Catégorie</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {actualites.map((actualite) => (
                      <TableRow key={actualite.id}>
                        <TableCell>{actualite.title}</TableCell>
                        <TableCell>{actualite.date}</TableCell>
                        <TableCell>{actualite.category}</TableCell>
                        <TableCell>
                          <Badge variant={actualite.status === "publié" ? "default" : "secondary"}>
                            {actualite.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(actualite)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(actualite.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="formations">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des formations</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une formation
              </Button>
            </div>
            {/* Contenu similaire pour les formations */}
          </TabsContent>

          <TabsContent value="emplois">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des emplois du temps</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter un emploi du temps
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Filière</TableHead>
                      <TableHead>Groupe</TableHead>
                      <TableHead>Nom du fichier</TableHead>
                      <TableHead>Date d'upload</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {emploisDuTemps.map((emploi) => (
                      <TableRow key={emploi.id}>
                        <TableCell>
                          <Badge variant="outline">{emploi.filiereLabel}</Badge>
                        </TableCell>
                        <TableCell>{emploi.groupeLabel}</TableCell>
                        <TableCell>{emploi.fileName}</TableCell>
                        <TableCell>{emploi.uploadDate}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.open(emploi.fileUrl, '_blank')}
                              title="Voir le fichier"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(emploi)}
                              title="Modifier"
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(emploi.id)}
                              title="Supprimer"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                {emploisDuTemps.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    Aucun emploi du temps trouvé. Cliquez sur "Ajouter un emploi du temps" pour commencer.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingItem ? "Modifier" : "Ajouter"} un élément
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSave} className="space-y-4">
              <div className="space-y-4">
                {activeTab === "actualites" && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="title">Titre</Label>
                      <Input
                        id="title"
                        defaultValue={editingItem?.title}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="content">Contenu</Label>
                      <Textarea
                        id="content"
                        defaultValue={editingItem?.content}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category">Catégorie</Label>
                      <Select defaultValue={editingItem?.category}>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner une catégorie" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="evenement">Événement</SelectItem>
                          <SelectItem value="formation">Formation</SelectItem>
                          <SelectItem value="infrastructure">Infrastructure</SelectItem>
                          <SelectItem value="partenariat">Partenariat</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}

                {activeTab === "emplois" && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="filiere">Filière</Label>
                      <Select value={selectedFiliere} onValueChange={handleFiliereChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner une filière" />
                        </SelectTrigger>
                        <SelectContent>
                          {filieres.map((filiere) => (
                            <SelectItem key={filiere.value} value={filiere.value}>
                              {filiere.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {selectedFiliere && (
                      <div className="space-y-2">
                        <Label htmlFor="groupe">Groupe</Label>
                        <Select value={selectedGroupe} onValueChange={setSelectedGroupe}>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un groupe" />
                          </SelectTrigger>
                          <SelectContent>
                            {getSelectedGroupes().map((groupe) => (
                              <SelectItem key={groupe.value} value={groupe.value}>
                                {groupe.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="file">Fichier emploi du temps</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          id="file"
                          type="file"
                          accept="image/*,.pdf"
                          onChange={handleFileChange}
                          className="flex-1"
                        />
                        <Upload className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Formats acceptés : PNG, JPG, JPEG, PDF (max 10MB)
                      </p>
                    </div>

                    {previewUrl && (
                      <div className="space-y-2">
                        <Label>Aperçu</Label>
                        <div className="border rounded-lg p-4 bg-muted/50">
                          {selectedFile?.type?.startsWith('image/') || previewUrl.includes('.png') || previewUrl.includes('.jpg') ? (
                            <img
                              src={previewUrl}
                              alt="Aperçu emploi du temps"
                              className="max-w-full h-auto max-h-64 mx-auto rounded"
                            />
                          ) : (
                            <div className="text-center py-8">
                              <Download className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                              <p className="text-sm text-muted-foreground">
                                Fichier PDF - Cliquez pour télécharger
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
              <DialogFooter>
                <Button type="submit">
                  {editingItem ? "Enregistrer les modifications" : "Ajouter"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </main>
    </div>
  );
};

export default Admin;
