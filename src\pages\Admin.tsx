
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Plus, PenSquare, Trash2 } from "lucide-react";

const Admin = () => {
  const { toast } = useToast();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [loginData, setLoginData] = useState({
    email: "",
    password: ""
  });
  const [activeTab, setActiveTab] = useState("actualites");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState(null);

  // Données d'exemple
  const actualites = [
    { id: 1, title: "Nouvelle formation", date: "2024-03-20", category: "formation", status: "publié" },
    { id: 2, title: "Événement portes ouvertes", date: "2024-04-15", category: "evenement", status: "brouillon" }
  ];

  const handleLogin = (e) => {
    e.preventDefault();
    // Vérification des identifiants (à implémenter avec le backend)
    setIsLoggedIn(true);
    toast({
      title: "Connexion réussie",
      description: "Bienvenue dans l'interface d'administration."
    });
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setLoginData({ email: "", password: "" });
  };

  const handleAdd = () => {
    setEditingItem(null);
    setIsAddDialogOpen(true);
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setIsAddDialogOpen(true);
  };

  const handleDelete = (id) => {
    // Logique de suppression (à implémenter avec le backend)
    toast({
      title: "Élément supprimé",
      description: "L'élément a été supprimé avec succès."
    });
  };

  const handleSave = (e) => {
    e.preventDefault();
    // Logique de sauvegarde (à implémenter avec le backend)
    setIsAddDialogOpen(false);
    setEditingItem(null);
    toast({
      title: "Modifications enregistrées",
      description: "Les changements ont été sauvegardés avec succès."
    });
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Accès Administrateur</CardTitle>
              <CardDescription>
                Veuillez vous authentifier avec vos identifiants.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={loginData.email}
                    onChange={(e) => setLoginData({ ...loginData, email: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Mot de passe</Label>
                  <Input
                    id="password"
                    type="password"
                    value={loginData.password}
                    onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                    required
                  />
                </div>
                <Button type="submit" className="w-full">Se connecter</Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-3xl font-bold text-gray-900">Interface d'administration</h1>
            <Button onClick={handleLogout} variant="outline">Se déconnecter</Button>
          </div>
        </div>
      </div>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 gap-4 mb-8">
            <TabsTrigger value="actualites">Actualités</TabsTrigger>
            <TabsTrigger value="formations">Formations</TabsTrigger>
            <TabsTrigger value="etudiants">Étudiants</TabsTrigger>
            <TabsTrigger value="emplois">Emplois du temps</TabsTrigger>
          </TabsList>

          <TabsContent value="actualites">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des actualités</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une actualité
              </Button>
            </div>
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Titre</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Catégorie</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {actualites.map((actualite) => (
                      <TableRow key={actualite.id}>
                        <TableCell>{actualite.title}</TableCell>
                        <TableCell>{actualite.date}</TableCell>
                        <TableCell>{actualite.category}</TableCell>
                        <TableCell>
                          <Badge variant={actualite.status === "publié" ? "default" : "secondary"}>
                            {actualite.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEdit(actualite)}
                            >
                              <PenSquare className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDelete(actualite.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="formations">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des formations</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter une formation
              </Button>
            </div>
            {/* Contenu similaire pour les formations */}
          </TabsContent>

          <TabsContent value="etudiants">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des étudiants</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter un étudiant
              </Button>
            </div>
            {/* Contenu similaire pour les étudiants */}
          </TabsContent>

          <TabsContent value="emplois">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Gestion des emplois du temps</h2>
              <Button onClick={handleAdd}>
                <Plus className="mr-2 h-4 w-4" /> Ajouter un emploi du temps
              </Button>
            </div>
            {/* Contenu similaire pour les emplois du temps */}
          </TabsContent>
        </Tabs>

        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingItem ? "Modifier" : "Ajouter"} un élément
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSave} className="space-y-4">
              <div className="space-y-4">
                {activeTab === "actualites" && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="title">Titre</Label>
                      <Input
                        id="title"
                        defaultValue={editingItem?.title}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="content">Contenu</Label>
                      <Textarea
                        id="content"
                        defaultValue={editingItem?.content}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category">Catégorie</Label>
                      <Select defaultValue={editingItem?.category}>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner une catégorie" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="evenement">Événement</SelectItem>
                          <SelectItem value="formation">Formation</SelectItem>
                          <SelectItem value="infrastructure">Infrastructure</SelectItem>
                          <SelectItem value="partenariat">Partenariat</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
              </div>
              <DialogFooter>
                <Button type="submit">
                  {editingItem ? "Enregistrer les modifications" : "Ajouter"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </main>
    </div>
  );
};

export default Admin;
