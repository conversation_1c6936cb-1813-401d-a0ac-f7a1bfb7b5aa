<?php
require_once 'config/database.php';

$page_title = 'Accueil';

// Récupérer les dernières actualités
$stmt = $pdo->prepare("SELECT * FROM v_actualites_completes WHERE statut = 'publie' ORDER BY date_publication DESC LIMIT 3");
$stmt->execute();
$actualites = $stmt->fetchAll();

// Récupérer les formations populaires
$stmt = $pdo->prepare("SELECT * FROM v_formations_completes WHERE statut = 'publie' AND badge = 'Populaire' LIMIT 3");
$stmt->execute();
$formations = $stmt->fetchAll();

// Récupérer les prochains événements
$stmt = $pdo->prepare("SELECT * FROM v_evenements_complets WHERE statut = 'publie' AND date_evenement >= CURDATE() ORDER BY date_evenement ASC LIMIT 3");
$stmt->execute();
$evenements = $stmt->fetchAll();

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="bg-gradient-to-r from-green-600 to-green-800 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
            Pôle Agriculture
        </h1>
        <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Formez-vous aux métiers de l'agriculture moderne et contribuez au développement durable du secteur agricole marocain
        </p>
        <div class="space-x-4">
            <a href="formations.php" class="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition">
                Découvrir nos formations
            </a>
            <a href="actualites.php" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition">
                Actualités
            </a>
        </div>
    </div>
</section>

<!-- Statistiques -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <?php
            // Récupérer les statistiques
            $stats = [
                ['icon' => 'fas fa-graduation-cap', 'number' => $pdo->query("SELECT COUNT(*) FROM formations WHERE statut = 'publie'")->fetchColumn(), 'label' => 'Formations'],
                ['icon' => 'fas fa-users', 'number' => $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'etudiant'")->fetchColumn(), 'label' => 'Étudiants'],
                ['icon' => 'fas fa-chalkboard-teacher', 'number' => $pdo->query("SELECT COUNT(*) FROM users WHERE role = 'formateur'")->fetchColumn(), 'label' => 'Formateurs'],
                ['icon' => 'fas fa-calendar-alt', 'number' => $pdo->query("SELECT COUNT(*) FROM evenements WHERE statut = 'publie'")->fetchColumn(), 'label' => 'Événements']
            ];
            
            foreach ($stats as $stat): ?>
                <div class="p-6">
                    <div class="text-4xl text-green-600 mb-4">
                        <i class="<?php echo $stat['icon']; ?>"></i>
                    </div>
                    <div class="text-3xl font-bold text-gray-800 mb-2"><?php echo $stat['number']; ?></div>
                    <div class="text-gray-600"><?php echo $stat['label']; ?></div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Dernières actualités -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">Dernières Actualités</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">Restez informé des dernières nouvelles et événements du Pôle Agriculture</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <?php foreach ($actualites as $actualite): ?>
                <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition">
                    <?php if ($actualite['image_url']): ?>
                        <img src="<?php echo htmlspecialchars($actualite['image_url']); ?>" alt="<?php echo htmlspecialchars($actualite['titre']); ?>" class="w-full h-48 object-cover">
                    <?php endif; ?>
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                <?php echo htmlspecialchars($actualite['categorie_nom']); ?>
                            </span>
                            <span class="text-gray-500 text-sm ml-auto"><?php echo date('d/m/Y', strtotime($actualite['date_publication'])); ?></span>
                        </div>
                        <h3 class="text-xl font-semibold mb-3 text-gray-800">
                            <?php echo htmlspecialchars($actualite['titre']); ?>
                        </h3>
                        <p class="text-gray-600 mb-4">
                            <?php echo htmlspecialchars(substr($actualite['resume'], 0, 120)) . '...'; ?>
                        </p>
                        <a href="actualite.php?id=<?php echo $actualite['id']; ?>" class="text-green-600 font-medium hover:text-green-700">
                            Lire la suite →
                        </a>
                    </div>
                </article>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-12">
            <a href="actualites.php" class="bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition">
                Voir toutes les actualités
            </a>
        </div>
    </div>
</section>

<!-- Formations populaires -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">Formations Populaires</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">Découvrez nos formations les plus demandées</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <?php foreach ($formations as $formation): ?>
                <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition">
                    <div class="flex items-center justify-between mb-4">
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            <?php echo htmlspecialchars($formation['categorie_nom']); ?>
                        </span>
                        <?php if ($formation['badge']): ?>
                            <span class="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                                <?php echo htmlspecialchars($formation['badge']); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-gray-800">
                        <?php echo htmlspecialchars($formation['titre']); ?>
                    </h3>
                    <p class="text-gray-600 mb-4">
                        <?php echo htmlspecialchars(substr($formation['description'], 0, 120)) . '...'; ?>
                    </p>
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <span><i class="fas fa-clock mr-1"></i><?php echo htmlspecialchars($formation['duree']); ?></span>
                        <span><i class="fas fa-users mr-1"></i><?php echo $formation['places_max']; ?> places</span>
                    </div>
                    <a href="formation.php?id=<?php echo $formation['id']; ?>" class="block w-full text-center bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition">
                        En savoir plus
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
