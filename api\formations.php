<?php
// API pour récupérer les formations
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

switch($method) {
    case 'GET':
        // Récupérer toutes les formations
        $query = "SELECT f.*, c.code as category_code, fil.code as filiere_code,
                  GROUP_CONCAT(fm.nom ORDER BY fm.ordre_affichage SEPARATOR '|') as modules
                  FROM formations f 
                  LEFT JOIN categories c ON f.category_id = c.id 
                  LEFT JOIN filieres fil ON f.filiere_id = fil.id
                  LEFT JOIN formation_modules fm ON f.id = fm.formation_id
                  GROUP BY f.id
                  ORDER BY f.date_creation DESC";
        
        $stmt = $db->prepare($query);
        $stmt->execute();
        $formations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Convertir les modules en tableau
        foreach ($formations as &$formation) {
            $formation['modules'] = $formation['modules'] ? explode('|', $formation['modules']) : [];
        }
        
        echo json_encode([
            'status' => 'success',
            'data' => $formations,
            'count' => count($formations)
        ]);
        break;
        
    case 'POST':
        // Ajouter une nouvelle formation
        $data = json_decode(file_get_contents("php://input"), true);
        
        // Trouver l'ID de la catégorie
        $category_id = null;
        if (isset($data['category'])) {
            $cat_query = "SELECT id FROM categories WHERE code = :category AND type = 'formation'";
            $cat_stmt = $db->prepare($cat_query);
            $cat_stmt->bindParam(':category', $data['category']);
            $cat_stmt->execute();
            $category = $cat_stmt->fetch();
            if ($category) {
                $category_id = $category['id'];
            }
        }
        
        // Trouver l'ID de la filière
        $filiere_id = null;
        if (isset($data['category'])) {
            $fil_query = "SELECT id FROM filieres WHERE code = :code";
            $fil_stmt = $db->prepare($fil_query);
            $fil_stmt->bindParam(':code', $data['category']);
            $fil_stmt->execute();
            $filiere = $fil_stmt->fetch();
            if ($filiere) {
                $filiere_id = $filiere['id'];
            }
        }
        
        $query = "INSERT INTO formations (titre, description, category_id, niveau, duree, prerequis, debouches, 
                  date_debut, places_max, frais, statut, badge, image_url, filiere_id) 
                  VALUES (:titre, :description, :category_id, :niveau, :duree, :prerequis, :debouches, 
                  :date_debut, :places_max, :frais, :statut, :badge, :image_url, :filiere_id)";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':titre', $data['title']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':niveau', $data['level']);
        $stmt->bindParam(':duree', $data['duration']);
        $stmt->bindParam(':prerequis', $data['prerequisites']);
        $stmt->bindParam(':debouches', $data['outcomes']);
        $stmt->bindParam(':date_debut', $data['startDate']);
        $stmt->bindParam(':places_max', $data['places']);
        $stmt->bindParam(':frais', $data['fees']);
        $stmt->bindParam(':statut', $data['status']);
        $stmt->bindParam(':badge', $data['badge']);
        $stmt->bindParam(':image_url', $data['image']);
        $stmt->bindParam(':filiere_id', $filiere_id);
        
        if($stmt->execute()) {
            $formation_id = $db->lastInsertId();
            
            // Ajouter les modules
            if (isset($data['modules']) && is_array($data['modules'])) {
                $module_query = "INSERT INTO formation_modules (formation_id, nom, ordre_affichage) VALUES (:formation_id, :nom, :ordre)";
                $module_stmt = $db->prepare($module_query);
                
                foreach ($data['modules'] as $index => $module) {
                    if (trim($module)) {
                        $module_stmt->bindParam(':formation_id', $formation_id);
                        $module_stmt->bindParam(':nom', $module);
                        $module_stmt->bindParam(':ordre', $index);
                        $module_stmt->execute();
                    }
                }
            }
            
            echo json_encode([
                'status' => 'success',
                'message' => 'Formation ajoutée avec succès',
                'id' => $formation_id
            ]);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => 'Erreur lors de l\'ajout de la formation'
            ]);
        }
        break;
        
    case 'PUT':
        // Modifier une formation
        $data = json_decode(file_get_contents("php://input"), true);
        $id = $_GET['id'] ?? null;
        
        if (!$id) {
            echo json_encode(['status' => 'error', 'message' => 'ID manquant']);
            break;
        }
        
        // Trouver l'ID de la catégorie
        $category_id = null;
        if (isset($data['category'])) {
            $cat_query = "SELECT id FROM categories WHERE code = :category AND type = 'formation'";
            $cat_stmt = $db->prepare($cat_query);
            $cat_stmt->bindParam(':category', $data['category']);
            $cat_stmt->execute();
            $category = $cat_stmt->fetch();
            if ($category) {
                $category_id = $category['id'];
            }
        }
        
        // Trouver l'ID de la filière
        $filiere_id = null;
        if (isset($data['category'])) {
            $fil_query = "SELECT id FROM filieres WHERE code = :code";
            $fil_stmt = $db->prepare($fil_query);
            $fil_stmt->bindParam(':code', $data['category']);
            $fil_stmt->execute();
            $filiere = $fil_stmt->fetch();
            if ($filiere) {
                $filiere_id = $filiere['id'];
            }
        }
        
        $query = "UPDATE formations SET titre = :titre, description = :description, category_id = :category_id, 
                  niveau = :niveau, duree = :duree, prerequis = :prerequis, debouches = :debouches, 
                  date_debut = :date_debut, places_max = :places_max, frais = :frais, statut = :statut, 
                  badge = :badge, image_url = :image_url, filiere_id = :filiere_id 
                  WHERE id = :id";
        
        $stmt = $db->prepare($query);
        $stmt->bindParam(':titre', $data['title']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':category_id', $category_id);
        $stmt->bindParam(':niveau', $data['level']);
        $stmt->bindParam(':duree', $data['duration']);
        $stmt->bindParam(':prerequis', $data['prerequisites']);
        $stmt->bindParam(':debouches', $data['outcomes']);
        $stmt->bindParam(':date_debut', $data['startDate']);
        $stmt->bindParam(':places_max', $data['places']);
        $stmt->bindParam(':frais', $data['fees']);
        $stmt->bindParam(':statut', $data['status']);
        $stmt->bindParam(':badge', $data['badge']);
        $stmt->bindParam(':image_url', $data['image']);
        $stmt->bindParam(':filiere_id', $filiere_id);
        $stmt->bindParam(':id', $id);
        
        if($stmt->execute()) {
            // Supprimer les anciens modules
            $delete_modules = "DELETE FROM formation_modules WHERE formation_id = :formation_id";
            $delete_stmt = $db->prepare($delete_modules);
            $delete_stmt->bindParam(':formation_id', $id);
            $delete_stmt->execute();
            
            // Ajouter les nouveaux modules
            if (isset($data['modules']) && is_array($data['modules'])) {
                $module_query = "INSERT INTO formation_modules (formation_id, nom, ordre_affichage) VALUES (:formation_id, :nom, :ordre)";
                $module_stmt = $db->prepare($module_query);
                
                foreach ($data['modules'] as $index => $module) {
                    if (trim($module)) {
                        $module_stmt->bindParam(':formation_id', $id);
                        $module_stmt->bindParam(':nom', $module);
                        $module_stmt->bindParam(':ordre', $index);
                        $module_stmt->execute();
                    }
                }
            }
            
            echo json_encode(['status' => 'success', 'message' => 'Formation modifiée avec succès']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Erreur lors de la modification']);
        }
        break;
        
    case 'DELETE':
        // Supprimer une formation
        $id = $_GET['id'] ?? null;
        
        if (!$id) {
            echo json_encode(['status' => 'error', 'message' => 'ID manquant']);
            break;
        }
        
        // Supprimer d'abord les modules (cascade)
        $delete_modules = "DELETE FROM formation_modules WHERE formation_id = :formation_id";
        $delete_stmt = $db->prepare($delete_modules);
        $delete_stmt->bindParam(':formation_id', $id);
        $delete_stmt->execute();
        
        // Supprimer la formation
        $query = "DELETE FROM formations WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);
        
        if($stmt->execute()) {
            echo json_encode(['status' => 'success', 'message' => 'Formation supprimée avec succès']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Erreur lors de la suppression']);
        }
        break;
        
    default:
        echo json_encode([
            'status' => 'error',
            'message' => 'Méthode non autorisée'
        ]);
        break;
}
?>
