// Service API pour communiquer avec le backend
const API_BASE_URL = 'http://localhost/api';

// Configuration par défaut pour les requêtes
const defaultHeaders = {
  'Content-Type': 'application/json',
};

// Fonction utilitaire pour les requêtes
async function apiRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const config: RequestInit = {
    headers: defaultHeaders,
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'Erreur API');
    }
    
    return data;
  } catch (error) {
    console.error('Erreur API:', error);
    throw error;
  }
}

// =====================================================
// ACTUALITÉS
// =====================================================
export const actualitesAPI = {
  // Récupérer toutes les actualités
  getAll: () => apiRequest('/actualites.php'),
  
  // Ajouter une actualité
  create: (data: any) => apiRequest('/actualites.php', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Modifier une actualité
  update: (id: number, data: any) => apiRequest(`/actualites.php?id=${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  
  // Supprimer une actualité
  delete: (id: number) => apiRequest(`/actualites.php?id=${id}`, {
    method: 'DELETE',
  }),
};

// =====================================================
// FORMATIONS
// =====================================================
export const formationsAPI = {
  // Récupérer toutes les formations
  getAll: () => apiRequest('/formations.php'),
  
  // Ajouter une formation
  create: (data: any) => apiRequest('/formations.php', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Modifier une formation
  update: (id: number, data: any) => apiRequest(`/formations.php?id=${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  
  // Supprimer une formation
  delete: (id: number) => apiRequest(`/formations.php?id=${id}`, {
    method: 'DELETE',
  }),
};

// =====================================================
// ÉVÉNEMENTS
// =====================================================
export const evenementsAPI = {
  // Récupérer tous les événements
  getAll: () => apiRequest('/evenements.php'),
  
  // Ajouter un événement
  create: (data: any) => apiRequest('/evenements.php', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  
  // Modifier un événement
  update: (id: number, data: any) => apiRequest(`/evenements.php?id=${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  
  // Supprimer un événement
  delete: (id: number) => apiRequest(`/evenements.php?id=${id}`, {
    method: 'DELETE',
  }),
};

// =====================================================
// TEST DE CONNEXION
// =====================================================
export const testAPI = {
  // Tester la connexion à la base de données
  testConnection: () => apiRequest('/test-connection.php'),
};

// =====================================================
// UTILITAIRES
// =====================================================
export const apiUtils = {
  // Vérifier si l'API est accessible
  isApiAvailable: async (): Promise<boolean> => {
    try {
      await testAPI.testConnection();
      return true;
    } catch {
      return false;
    }
  },
  
  // Formater les erreurs API
  formatError: (error: any): string => {
    if (error.message) {
      return error.message;
    }
    return 'Une erreur est survenue lors de la communication avec le serveur';
  },
};

export default {
  actualites: actualitesAPI,
  formations: formationsAPI,
  evenements: evenementsAPI,
  test: testAPI,
  utils: apiUtils,
};
