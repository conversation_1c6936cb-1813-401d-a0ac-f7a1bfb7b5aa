# Guide complet - Gestion des formations (Admin)

## Vue d'ensemble

La section "Formations" de l'interface d'administration permet de gérer l'intégralité du catalogue de formations du Pôle Agriculture CMC. Cette interface offre un contrôle complet sur les programmes de formation, leurs contenus, modalités et conditions d'admission.

## 📚 Fonctionnalités disponibles

### Visualisation des formations
- **Tableau complet** avec toutes les informations essentielles
- **Colonnes affichées** :
  - **Formation** : Titre, description et badge (si applicable)
  - **Catégorie** : Type de formation avec badge coloré
  - **Niveau** : Technicien, Technicien Spécialisé, Qualification
  - **Durée** : Durée totale de la formation
  - **Places** : Nombre de places disponibles
  - **Début** : Date de début de session
  - **Statut** : Publié/Brouillon
  - **Actions** : Voir, Modifier, Supprimer

### Ajout d'une nouvelle formation

#### Informations générales
1. **Titre de la formation** : Nom officiel du programme
2. **Description** : Présentation complète de la formation
3. **Catégorie** : 
   - Culture (production végétale)
   - Élevage (production animale)
   - Gestion d'exploitation
   - Agriculture durable
4. **Niveau** :
   - Technicien (niveau bac)
   - Technicien Spécialisé (niveau bac+2)
   - Qualification (formation courte)

#### Modalités pratiques
5. **Durée** : Durée totale (ex: "24 mois", "18 mois")
6. **Nombre de places** : Capacité d'accueil (1-50 places)
7. **Frais** : Coût de la formation en MAD
8. **Date de début** : Session de démarrage (ex: "Septembre 2025")
9. **Statut** : Brouillon (non visible) ou Publié (visible sur le site)

#### Contenu pédagogique
10. **Prérequis** : Conditions d'admission et niveau requis
11. **Débouchés** : Opportunités professionnelles après la formation
12. **Badge** : Label optionnel (ex: "Populaire", "Nouvelle session")
13. **Image** : URL de l'image d'illustration

#### Modules de formation (Gestion dynamique)
- **Ajout de modules** : Interface pour ajouter des modules un par un
- **Modification** : Édition directe des noms de modules
- **Suppression** : Retrait de modules individuels
- **Validation** : Impossible de supprimer le dernier module

## 🎯 Gestion des modules - Fonctionnalité avancée

### Interface dynamique
- **Liste éditable** : Chaque module peut être modifié directement
- **Ajout en temps réel** : Nouveau module ajouté instantanément
- **Suppression sécurisée** : Bouton de suppression pour chaque module
- **Validation** : Empêche la suppression du dernier module

### Utilisation
1. **Modifier un module existant** : Cliquer dans le champ et modifier le texte
2. **Ajouter un module** : 
   - Saisir le nom dans le champ "Ajouter un nouveau module"
   - Cliquer sur le bouton "+" ou appuyer sur Entrée
3. **Supprimer un module** : Cliquer sur l'icône poubelle à droite du module

### Bonnes pratiques pour les modules
- **Noms clairs** : Utiliser des intitulés précis et professionnels
- **Ordre logique** : Organiser les modules dans un ordre pédagogique
- **Complétude** : Inclure tous les aspects importants de la formation

## 📊 Données d'exemple incluses

### 3 formations pré-configurées :

#### 1. Technicien en Production Végétale
- **Catégorie** : Culture
- **Niveau** : Technicien
- **Durée** : 24 mois
- **8 modules** : De l'agronomie générale à la post-récolte
- **Statut** : Publié

#### 2. Technicien Spécialisé en Élevage
- **Catégorie** : Élevage
- **Niveau** : Technicien Spécialisé
- **Durée** : 30 mois
- **8 modules** : De la zootechnie à la commercialisation
- **Statut** : Publié

#### 3. Gestionnaire d'Exploitation Agricole
- **Catégorie** : Gestion
- **Niveau** : Technicien Spécialisé
- **Durée** : 24 mois
- **8 modules** : De l'économie agricole à la législation
- **Statut** : Brouillon

## 🔧 Fonctionnalités techniques

### Validation des données
- **Champs obligatoires** : Vérification avant sauvegarde
- **Types de données** : Validation des nombres (places, frais)
- **Modules** : Au moins un module requis
- **URLs** : Validation des liens d'images

### Gestion d'état avancée
- **État des modules** : Gestion dynamique de la liste
- **Formulaires** : Pré-remplissage pour l'édition
- **Reset automatique** : Nettoyage après sauvegarde

### Interface utilisateur
- **Badges visuels** : Identification rapide des catégories et statuts
- **Actions intuitives** : Icônes claires pour chaque action
- **Feedback** : Notifications de confirmation
- **Responsive** : Adaptation aux différentes tailles d'écran

## 🎨 Catégories et leurs spécificités

### Culture (Production végétale)
- **Focus** : Cultures maraîchères, céréalières, arboricoles
- **Modules typiques** : Agronomie, protection phytosanitaire, irrigation
- **Débouchés** : Exploitations agricoles, coopératives, conseil

### Élevage (Production animale)
- **Focus** : Bovins, ovins, caprins, aviculture, apiculture
- **Modules typiques** : Zootechnie, nutrition animale, pathologie
- **Débouchés** : Élevages, industries agroalimentaires, vétérinaire

### Gestion d'exploitation
- **Focus** : Aspects économiques et managériaux
- **Modules typiques** : Comptabilité, marketing, gestion RH
- **Débouchés** : Direction d'exploitation, conseil, coopératives

### Agriculture durable
- **Focus** : Pratiques respectueuses de l'environnement
- **Modules typiques** : Agroécologie, certification bio, énergies renouvelables
- **Débouchés** : Agriculture biologique, développement durable

## 🚀 Workflow recommandé

### Création d'une nouvelle formation
1. **Planification** : Définir les objectifs et le public cible
2. **Création en brouillon** : Saisir les informations de base
3. **Développement des modules** : Structurer le contenu pédagogique
4. **Révision** : Vérifier toutes les informations
5. **Publication** : Changer le statut pour rendre visible

### Maintenance des formations existantes
1. **Révision périodique** : Mettre à jour les contenus
2. **Adaptation** : Ajuster selon les retours des stagiaires
3. **Évolution** : Ajouter de nouveaux modules si nécessaire
4. **Archivage** : Passer en brouillon les formations obsolètes

## 📈 Intégration avec le site

### Synchronisation automatique
- **Affichage public** : Les formations publiées apparaissent sur `/formations`
- **Filtrage** : Possibilité de filtrer par catégorie côté public
- **Détails** : Toutes les informations sont accessibles aux visiteurs
- **Inscription** : Formulaires d'inscription intégrés

### Cohérence des données
- **Images** : URLs d'images professionnelles recommandées
- **Descriptions** : Textes clairs et attractifs pour le public
- **Modules** : Liste complète pour informer les candidats
- **Prérequis** : Informations précises pour l'orientation

Cette interface de gestion des formations offre un contrôle complet et professionnel sur l'offre de formation du Pôle Agriculture, permettant une gestion efficace et une présentation attractive des programmes.
